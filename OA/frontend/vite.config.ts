import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import type { ConfigEnv, UserConfig } from 'vite'

// https://vitejs.dev/config/
export default defineConfig(({ mode }: ConfigEnv): UserConfig => {
  // 在开发模式下默认为本地调试
  const isLocal = mode === 'development';

  // 根据环境设置不同的目标服务器
  const localSgTarget = "http://local.test.hitox.net:8001";
  const localFlowTarget = "http://local.test.hitox.net:5001";
  const serverTarget = "http://test.hitox.net:31114";

  // 输出构建信息
  // console.log(`构建环境: ${mode}, 使用${isLocal ? '本地' : '服务器'}配置`);

  return {
    base: '/fe-sg/',
    plugins: [react()],
    server: {
      host: 'local.test.hitox.net',
      port: 5173,
      open: true,
      proxy: {
        "/hitox/sg": {
          target: isLocal ? localSgTarget : serverTarget,
          changeOrigin: true
        },
        "/hitox": {
          target: serverTarget,
          changeOrigin: true
        },
        "/fe-personal-center": {
          target: serverTarget,
          changeOrigin: true
        },
        "/oss/user-avatar": {
          target: serverTarget,
          changeOrigin: true
        },
        "/hitoflow/web": {
          target: localFlowTarget,
          changeOrigin: true
        },
        "/hitoflow/server": {
          target: serverTarget,
          changeOrigin: true
        },
      }
    }
  }
})
