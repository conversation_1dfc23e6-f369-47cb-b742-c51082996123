import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Dropdown, Avatar, Badge, Input, Drawer, message, Spin } from 'antd';
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  DashboardOutlined,
  RobotOutlined,
  ApartmentOutlined,
  BookOutlined,
  MessageOutlined,
  FileOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  QuestionCircleOutlined,
  SearchOutlined,
  MenuOutlined,
  LogoutOutlined
} from '@ant-design/icons';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { useDeviceDetect } from '../hooks/useDeviceDetect';
import MobileNavBar from '../components/MobileNavBar';
import { useAuth } from '../contexts/AuthContext';
import '../styles/layouts/MainLayout.less';
import axios from 'axios';

const { Header, Sider, Content, Footer } = Layout;
const { Search } = Input;

interface MenuItem {
  key: string;
  icon?: React.ReactNode;
  label: string;
  children?: MenuItem[];
}

const MainLayout = ({ powerMenus = [] }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const [searchVisible, setSearchVisible] = useState(false);
  const { isMobile, isTablet } = useDeviceDetect();
  const navigate = useNavigate();
  const location = useLocation();
  const { user, isLoading } = useAuth();
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [tenantDetail, setTenantDetail] = useState({ logo: '', title: '', tenantName: '' });

  type IconMapType = {
    [key: string]: React.ReactNode;
  };

  const iconMap: IconMapType = {
    '/dashboard': <DashboardOutlined />,
    '/ai-assistant': <RobotOutlined />,
    '/process': <ApartmentOutlined />,
    '/knowledge': <BookOutlined />,
    '/communication/chat': <MessageOutlined />,
    '/file-management': <FileOutlined />,
    '/report': <BarChartOutlined />,
    '/settings': <SettingOutlined />
  }

  const funcRoutes = (menus: MenuItem[], routes: any[]) => {
    routes.map(route => {
      let menu = {
        key: route.path,
        icon: iconMap[route.path],
        label: route.title
      }
      menus.push(menu);
      if (route.routes && route.routes.length > 0) {
        menu.children = []
        funcRoutes(menu.children, route.routes)
      }
    })
  }

  const fetchMenus = () => {
    const menus: MenuItem[] = [];
    funcRoutes(menus, powerMenus);
    setMenuItems(menus);
  }

  const logout = () => {
    document.cookie = `hito-super-admin-user-token=;domain=${window.location.hostname};path=/`
    window.location.href = "/fe-personal-center/#/login"
  }

  useEffect(() => {
    if (isTablet) {
      setCollapsed(true);
    } else if (!isMobile && !collapsed) {
      setCollapsed(false);
    }
    if (isMobile) {
      setCollapsed(true);
    }
    fetchMenus()
    axios.get('/hitox/access-manager/tenant/tenantDetailOfUser').then(res => {
      if (res.status == 200 && res.data.success) {
        setTenantDetail(res.data.data);
      }
    })
  }, [isTablet, isMobile, powerMenus]);

  const getSelectedKeys = () => {
    const pathname = location.pathname;
    let selectedKey = pathname;
    for (const item of menuItems) {
      if (item.children) {
        const childMatch = item.children.find(child => pathname.startsWith(child.key));
        if (childMatch) {
          selectedKey = childMatch.key;
          break;
        }
      } else if (pathname.startsWith(item.key)) {
        selectedKey = item.key;
        break;
      }
    }
    if (selectedKey === '/' || !menuItems.some(item => item.key === selectedKey || (item.children && item.children.some(c => c.key === selectedKey)))) {
      selectedKey = '/dashboard';
    }

    return [selectedKey];
  };

  const getOpenKeys = () => {
    const pathname = location.pathname;
    const openKey = menuItems.find(item =>
      item.children && item.children.some(child => pathname.startsWith(child.key))
    )?.key;
    return openKey ? [openKey] : [];
  };

  const handleMenuClick = (e: { key: string }) => {
    navigate(e.key);
    if (isMobile) {
      setMobileMenuVisible(false);
    }
  };

  const userMenuItems = [
    {
      key: 'profile',
      label: '个人中心',
      icon: <UserOutlined />,
    },
    // {
    //   key: 'settings',
    //   label: '账号设置',
    //   icon: <SettingOutlined />,
    // },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LogoutOutlined />,
      danger: true,
    },
  ];

  const handleUserMenuClick = ({ key }: { key: string }) => {
    if (key === 'logout') {
      logout();
    } else if (key === 'profile') {
      window.location.href = "/fe-personal-center/"
    } else if (key === 'settings') {
      navigate('/settings/system');
    } else {
      message.info(`点击了菜单项 ${key}`);
    }
  };

  const notificationItems = [
    { key: '1', label: '您有一条新的审批请求' },
    { key: '2', label: '系统更新通知' },
    { key: '3', label: '张三 @了你' },
  ];

  if (isLoading) {
    return (
      <Layout style={{ minHeight: '100vh', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Spin size="large" tip="加载用户信息..." />
      </Layout>
    );
  }

  const displayName = user?.full_name || user?.username || '用户';

  return (
    <Layout className={`main-layout ${isMobile ? 'mobile-layout' : ''}`}>
      <Header className="header">
        <div className="logo">
          {isMobile && (
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => setMobileMenuVisible(true)}
              className="mobile-menu-button"
            />
          )}
          <img src={tenantDetail.logo} onClick={() => navigate('/')} style={{ cursor: 'pointer' }} />
          {!isMobile && <h1>{tenantDetail.title}</h1>}
        </div>
        {!isMobile && (
          <div className="header-center">
          </div>
        )}
        <div className="header-right">
          {isMobile && (
            <Button
              type="text"
              icon={<SearchOutlined />}
              onClick={() => setSearchVisible(!searchVisible)}
              className="mobile-search-button"
            />
          )}
          {!isMobile && (
            <>
              <Dropdown
                menu={{ items: notificationItems }}
                placement="bottomRight"
                arrow
                trigger={['click']}
              >
                <Badge count={3} size="small">
                  <Button
                    type="text"
                    icon={<BellOutlined />}
                    className="notification-button"
                  />
                </Badge>
              </Dropdown>
              <Button
                type="text"
                icon={<QuestionCircleOutlined />}
                className="help-button"
                onClick={() => message.info('帮助文档待实现')}
              />
            </>
          )}
          <Dropdown
            menu={{
              items: userMenuItems,
              onClick: handleUserMenuClick
            }}
            placement="bottomRight"
            arrow
            trigger={['click']}
          >
            <div className="user-info">
              <Avatar icon={<UserOutlined />} />
              <span className="username">{displayName}</span>
            </div>
          </Dropdown>
        </div>
      </Header>

      {isMobile && (
        <Drawer
          title="全局搜索"
          placement="top"
          closable={true}
          onClose={() => setSearchVisible(false)}
          open={searchVisible}
          height={120}
          className="mobile-search-drawer"
        >
          <Search
            placeholder="搜索文档、流程、联系人..."
            allowClear
            enterButton
            size="large"
          />
        </Drawer>
      )}

      <Layout>
        {!isMobile && (
          <Sider
            width={220}
            collapsible
            collapsed={collapsed}
            trigger={null}
            className="sidebar"
          >
            <div style={{ textAlign: collapsed ? 'center' : 'right', padding: '10px 0' }}>
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={() => setCollapsed(!collapsed)}
                className="trigger-button"
              />
            </div>
            <Menu
              mode="inline"
              selectedKeys={getSelectedKeys()}
              defaultOpenKeys={getOpenKeys()}
              items={menuItems}
              onClick={handleMenuClick}
              className="sidebar-menu"
            />
          </Sider>
        )}

        {isMobile && (
          <Drawer
            title="智能OA系统"
            placement="left"
            closable={true}
            onClose={() => setMobileMenuVisible(false)}
            open={mobileMenuVisible}
            width="80%"
            className="mobile-menu-drawer"
            bodyStyle={{ padding: 0 }}
          >
            <Menu
              mode="inline"
              selectedKeys={getSelectedKeys()}
              defaultOpenKeys={getOpenKeys()}
              items={menuItems}
              onClick={handleMenuClick}
              className="mobile-sidebar-menu"
            />
          </Drawer>
        )}

        <Layout className="content-layout">
          <Content className="content">
            <React.Suspense fallback={
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 'calc(100vh - 128px)' }}>
                <Spin size="large" />
              </div>
            }>
              <Outlet />
            </React.Suspense>
          </Content>
          {!isMobile && (
            <Footer className="footer">
              Powered By Hitox ©{new Date().getFullYear()}
            </Footer>
          )}
        </Layout>
      </Layout>

      {isMobile && <MobileNavBar />}
    </Layout>
  );
};

export default MainLayout;
