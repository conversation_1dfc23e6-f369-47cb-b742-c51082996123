import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  DashboardOutlined,
  RobotOutlined,
  ApartmentOutlined,
  BookOutlined,
  MessageOutlined,
  FileOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  BellOutlined,
  UserOutlined,
  QuestionCircleOutlined,
  SearchOutlined,
  MenuOutlined,
  LogoutOutlined
} from '@ant-design/icons';

const defaultMenuItems = [
  {
    key: '/dashboard',
    icon: <DashboardOutlined />,
    label: '工作台',
  },
  {
    key: '/ai-assistant',
    icon: <RobotOutlined />,
    label: '智能助手',
    children: [
      {
        key: '/ai-assistant/sentinel',
        label: 'AI哨兵',
      },
      {
        key: '/ai-assistant/guide',
        label: 'AI向导',
      },
    ],
  },
  {
    key: '/process',
    icon: <ApartmentOutlined />,
    label: '流程中心',
    children: [
      {
        key: '/process/my',
        label: '我的流程',
      },
      {
        key: '/process/templates',
        label: '流程模板',
      },
      {
        key: '/process/designer',
        label: '流程设计器',
      },
    ],
  },
  {
    key: '/knowledge',
    icon: <BookOutlined />,
    label: '知识管理',
    children: [
      {
        key: '/knowledge/new-base',
        label: '新知识库',
      },
      {
        key: '/knowledge/enterprise',
        label: '企业知识库',
      },
      {
        key: '/knowledge/documents',
        label: '文档中心',
      },
      {
        key: '/knowledge/search',
        label: '跨模态搜索',
      },
    ],
  },
  {
    key: '/communication/chat',
    icon: <MessageOutlined />,
    label: '沟通协作',
  },
  {
    key: '/file-management',
    icon: <FileOutlined />,
    label: '文件管理',
    children: [
      {
        key: '/file-management/processing',
        label: '文件处理',
      },
      {
        key: '/file-management/seal',
        label: '电子印章',
      },
    ],
  },
  {
    key: '/report',
    icon: <BarChartOutlined />,
    label: '报告中心',
    children: [
      {
        key: '/report/generation',
        label: '报告生成',
      },
      {
        key: '/report/templates',
        label: '报告模板',
      },
    ],
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: '系统设置',
    children: [
      {
        key: '/settings/personal',
        label: '个人设置',
      },
      {
        key: '/settings/department',
        label: '部门管理',
      },
      {
        key: '/settings/system',
        label: '系统配置',
      },
      {
        key: '/settings/dify-apps',
        label: 'Dify 应用管理',
      },
    ],
  },
];