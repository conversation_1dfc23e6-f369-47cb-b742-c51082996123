import React from 'react';
import { createRoot } from 'react-dom/client';
import { I18nextProvider } from 'react-i18next';
import i18n from './locales/i18n';
import App from './App';
import Cookies from 'js-cookie';

// 导入全局样式
import './styles/global.less';

const root = createRoot(document.getElementById('root')!);

const bToken = Cookies.get('hito-tenant-user-token');
if (!bToken) {
  window.location.href = "/fe-personal-center/#/login"
}

root.render(
  <React.StrictMode>
    <I18nextProvider i18n={i18n}>
      <App />
    </I18nextProvider>
  </React.StrictMode>
);
