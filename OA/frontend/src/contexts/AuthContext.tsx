import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom'; // 引入 useNavigate
import apiClient from '../services/apiClient';
import axios from 'axios';

// 定义 User 接口，与后端 UserResponse 对应
interface User {
  id: number;
  username: string;
  email?: string;
  full_name?: string;
  department?: string;
  position?: string;
  is_active: boolean;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  fetchUser: () => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 清除所有可能与Supabase相关的本地存储项
const clearAllAuthStorage = () => {
  // 清除标准认证相关项
  localStorage.removeItem('user');
  localStorage.removeItem('token');
  localStorage.removeItem('token_type');

  // 清除可能与Supabase相关的项
  localStorage.removeItem('supabase.auth.token');
  localStorage.removeItem('supabase.auth.expires_at');
  localStorage.removeItem('supabase.auth.refresh_token');
  localStorage.removeItem('sb-refresh-token');
  localStorage.removeItem('sb-access-token');
  localStorage.removeItem('sb-auth-token');

  // 清除其他可能存在的未使用的认证令牌
  const keysToRemove: string[] = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (
      key.includes('token') ||
      key.includes('auth') ||
      key.includes('refresh') ||
      key.includes('supabase')
    )) {
      keysToRemove.push(key);
    }
  }

  // 移除找到的键
  keysToRemove.forEach(key => localStorage.removeItem(key));
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate(); // 获取 navigate 函数

  // 组件首次加载时，清除所有可能导致不必要请求的认证存储
  useEffect(() => {
    clearAllAuthStorage();
  }, []);

  const fetchUser = async () => {
    // 如果已经在加载，则不再重复请求
    if (!isLoading) setIsLoading(true);
    try {
      // 调用后端 /users/me 获取用户信息
      const response = await apiClient.get<User>('/users/me');
      setUser(response.data);
      console.log("User data fetched:", response.data); // 添加日志
    } catch (error) {
      console.error('Failed to fetch user:', error);
      setUser(null); // 获取失败，清空用户信息
      // 在基于 Header 的认证中，获取失败可能意味着 Header 无效或后端出错
      // 此处不强制重定向到登录，因为认证可能仍然有效，只是获取用户信息失败
      // 但可以根据具体错误类型决定是否跳转
      // if (axios.isAxiosError(error) && error.response?.status === 401) {
      //   navigate('/login'); // 如果是401，跳转登录
      // }
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    console.log("Logging out..."); // 添加日志
    setUser(null);
    // 清除所有认证相关存储
    clearAllAuthStorage();
    axios.post('/hitox/access-manager/tenant/login/logout')
    // 跳转到首页
    window.location.href = "/fe-personal-center/#/login"
  };

  useEffect(() => {
    fetchUser(); // 组件挂载时自动获取用户信息
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 空依赖数组确保只在挂载时执行一次

  return (
    <AuthContext.Provider value={{ user, isLoading, fetchUser, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 