@import '../../theme.less';

.new-knowledge-base-container {
  height: 100%;
  display: flex;
  overflow: hidden;

  // 左侧知识库导航栏
  .knowledge-sidebar {
    width: 15%;
    background-color: #f5f5f5;
    border-right: 1px solid #e8e8e8;
    overflow-y: auto;
    padding: 16px 0;
    flex-shrink: 0;

    .sidebar-header {
      padding: 0 16px 16px;
      border-bottom: 1px solid #e8e8e8;
      margin-bottom: 16px;
    }

    // 新增：导航菜单样式
    .navigation-menu {
      margin-bottom: 16px;
      padding: 0 8px;

      .nav-item {
        display: flex;
        align-items: center;
        padding: 12px 8px;
        margin: 4px 0;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s;
        color: @text-primary-color;

        &:hover {
          background-color: #e6f7ff;
          color: @primary-color;
        }

        &.active {
          background-color: @primary-color;
          color: #fff;

          .nav-icon {
            color: #fff;
          }
        }

        .nav-icon {
          margin-right: 8px;
          font-size: 16px;
          color: @text-secondary-color;
          transition: color 0.3s;
        }

        span {
          font-weight: 500;
        }
      }
    }

    .knowledge-category {
      margin-bottom: 8px;

      .category-title {
        padding: 0 16px;
        margin-bottom: 4px;
        color: @text-secondary-color;
        font-weight: 500;
      }
    }

    .knowledge-item {
      padding: 8px 16px;
      cursor: pointer;
      transition: all 0.3s;
      margin: 2px 0;
      border-radius: 0 4px 4px 0;
      display: flex;
      align-items: center;
      position: relative;

      &:hover {
        background-color: #e6f7ff;

        .kb-actions {
          opacity: 1;
        }
      }

      &.active {
        background-color: #e6f7ff;
        border-left: 3px solid @primary-color;
      }

      .kb-content {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0; // 防止内容溢出

        .item-icon {
          margin-right: 8px;
          color: @primary-color;
          flex-shrink: 0;
        }

        .kb-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .item-count {
          margin-left: 8px;
          font-size: 12px;
          color: @text-secondary-color;
          background-color: #f0f0f0;
          padding: 2px 6px;
          border-radius: 10px;
          flex-shrink: 0;
        }
      }

      .kb-actions {
        opacity: 0;
        transition: opacity 0.3s;
        margin-left: 8px;
        flex-shrink: 0;

        .ant-btn {
          border: none;
          box-shadow: none;
          padding: 4px;
          height: auto;
          width: auto;
          min-width: auto;

          &:hover {
            background-color: rgba(255, 255, 255, 0.8);
          }
        }
      }
    }

    .create-button {
      margin: 16px;
      width: calc(100% - 32px);
    }
  }

  // 中间文档区域
  .document-content {
    width: 22.5%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #e8e8e8;
    overflow: hidden;

    .content-header {
      padding: 16px;
      border-bottom: 1px solid #e8e8e8;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #fff;
      flex-shrink: 0;
      height: 64px;
    }

    .document-list {
      flex: 1;
      overflow-y: auto;
      padding: 16px 16px 0;
      background-color: #f9f9f9;

      .document-item {
        background-color: #fff;
        border-radius: 4px;
        margin-bottom: 12px;
        padding: 12px;
        cursor: pointer;
        transition: all 0.3s;
        border: 1px solid #e8e8e8;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
          transform: translateY(-2px);
        }

        &.active {
          border-color: @primary-color;
          background-color: #f0f7ff;
        }

        .document-title {
          font-weight: 500;
          margin-bottom: 4px;
          color: @text-primary-color;
        }

        .document-meta {
          color: @text-secondary-color;
          font-size: 12px;

          // 处理中状态的动画样式
          .processing-text {
            display: flex;
            align-items: center;

            .processing-dots {
              display: inline-flex;

              .dot {
                opacity: 0;
                animation: dotFade 1.4s infinite;
                margin-left: 1px;

                &:nth-child(1) {
                  animation-delay: 0s;
                }

                &:nth-child(2) {
                  animation-delay: 0.2s;
                }

                &:nth-child(3) {
                  animation-delay: 0.4s;
                }
              }
            }
          }
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
      height: 100%;
      padding: 16px 16px 0;
      text-align: left;
      background-color: #f9f9f9;

      .upload-area {
        width: 100%;
        border: 2px dashed #d9d9d9;
        border-radius: 8px;
        padding: 24px;
        margin-bottom: 24px;
        transition: all 0.3s;
        background-color: #fff;
        text-align: center;

        &:hover {
          border-color: @primary-color;
        }

        .upload-icon {
          font-size: 48px;
          color: #d9d9d9;
          margin-bottom: 16px;
        }
      }

      .action-buttons {
        display: flex;
        gap: 12px;
        margin-top: 16px;
        margin-bottom: 24px;
      }
    }
  }

  // 右侧AI聊天区域
  .ai-chat {
    width: 62.5%;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    background-color: #fff;

    // 聊天机器人容器
    .chatbot-container {
      display: flex;
      flex-direction: column;
      height: 100%;

      // 模型选择器样式
      .model-selector {
        padding: 12px 16px;
        border-bottom: 1px solid #e8e8e8;
        background-color: #f9f9f9;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 50px;

        .ant-space {
          width: 100%;
        }
      }
    }

    .chat-header {
      padding: 16px;
      border-bottom: 1px solid #e8e8e8;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      height: 64px;

      .ai-icon {
        margin-right: 8px;
        color: @primary-color;
        font-size: 20px;
      }
    }

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 16px 16px 0;
      background-color: #f5f5f5;

      .message {
        margin-bottom: 16px;
        max-width: 85%;

        .message-content {
          padding: 10px 14px;
          border-radius: 8px;
          word-break: break-word;
        }

        &.user-message {
          margin-left: auto;

          .message-content {
            background-color: #e6f7ff;
            color: #333;
          }
        }

        &.ai-message {
          margin-right: auto;

          .message-content {
            background-color: #fff;
            color: #333;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }

    .chat-input {
      padding: 16px;
      border-top: 1px solid #e8e8e8;
      display: flex;
      align-items: center;
      background-color: #fff;
      flex-shrink: 0;

      .input-field {
        flex: 1;
        margin-right: 8px;
      }

      .send-button {
        flex-shrink: 0;
      }
    }
  }

  // 新增：知识库市场全屏内容区域
  .marketplace-full-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: #f9f9f9;

    .marketplace-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 24px;

      .marketplace-header {
        margin-bottom: 24px;
        text-align: center;

        h3 {
          margin-bottom: 8px;
          color: @text-primary-color;
        }
      }

      .marketplace-content {
        flex: 1;
        overflow-y: auto;

        .marketplace-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
          gap: 20px;
          padding-bottom: 20px;

          .marketplace-item {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s;
            border: 1px solid #e8e8e8;

            &:hover {
              box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
              transform: translateY(-2px);
            }

            .marketplace-item-header {
              margin-bottom: 16px;

              h5 {
                margin-bottom: 8px;
                color: @text-primary-color;
              }

              .marketplace-item-meta {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .rating {
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  font-size: 14px;
                  color: @text-secondary-color;
                }
              }
            }

            .marketplace-item-content {
              margin-bottom: 16px;

              .description {
                display: block;
                margin-bottom: 12px;
                line-height: 1.5;
                color: @text-secondary-color;
              }

              .marketplace-item-stats {
                margin-bottom: 12px;

                span {
                  color: @text-secondary-color;
                  font-size: 13px;
                  display: flex;
                  align-items: center;
                }
              }

              .marketplace-item-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 6px;

                .ant-tag {
                  margin: 0;
                  font-size: 12px;
                  padding: 2px 6px;
                  border-radius: 4px;
                }
              }
            }

            .marketplace-item-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding-top: 16px;
              border-top: 1px solid #f0f0f0;

              .ant-space {
                flex-shrink: 0;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: 1200px) {
  .new-knowledge-base-container {
    .knowledge-sidebar {
      width: 15%;
    }
    .document-content {
      width: 25%;
    }
    .ai-chat {
      width: 60%;
    }
  }
}

@media (max-width: 992px) {
  .new-knowledge-base-container {
    flex-direction: column;

    .knowledge-sidebar,
    .document-content,
    .ai-chat {
      width: 100%;
      height: auto;
    }

    .knowledge-sidebar {
      max-height: 200px;
    }

    .document-content {
      max-height: calc(100% - 400px);
    }

    .ai-chat {
      max-height: 400px;
    }
  }
}

// 定义点状动画关键帧
@keyframes dotFade {
  0%, 20% {
    opacity: 0;
  }
  50%, 70% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}