import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Input,
  Select,
  DatePicker,
  Space,
  Tabs,
  Badge,
  Tooltip,
  Modal,
  Steps,
  Timeline,
  message,
  Spin,
  Progress
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  HistoryOutlined,
  UserOutlined,
  CommentOutlined
} from '@ant-design/icons';
// import { useTranslation } from 'react-i18next';
import '../../styles/pages/process/MyProcess.less';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Step } = Steps;

interface ProcessItem {
  id: string;
  title: string;
  type: string;
  status: string;
  priority: string;
  applicant: string;
  department: string;
  createTime: string;
  updateTime: string;
  currentStep?: number;
  totalSteps?: number;
  created_at: string;
  updated_at: string;
  template_id: number;
  created_by: number;
  description: string;
}

// 状态映射
const STATUS_MAP: Record<string, { text: string; color: string }> = {
  DRAFT: { text: '草稿', color: 'default' },
  PENDING: { text: '审批中', color: 'processing' },
  APPROVED: { text: '已通过', color: 'success' },
  REJECTED: { text: '已拒绝', color: 'error' },
  CANCELLED: { text: '已取消', color: 'default' }
};

// 流程类型映射
const PROCESS_TYPES: Record<string, { text: string; color: string }> = {
  EXPENSE: { text: '报销申请', color: 'green' },
  LEAVE: { text: '休假申请', color: 'purple' },
  PURCHASE: { text: '采购申请', color: 'orange' },
  BUSINESS_TRIP: { text: '差旅申请', color: 'cyan' }
};

// 优先级映射
const PRIORITY_MAP: Record<string, { text: string; color: string }> = {
  HIGH: { text: '紧急', color: 'red' },
  MEDIUM: { text: '普通', color: 'orange' },
  LOW: { text: '低级', color: 'blue' }
};

// 格式化日期
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
};

const MyProcess: React.FC = () => {
  // 使用i18n进行国际化，后续会用到
  // 暂时注释掉，等需要时再启用
  // const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('all');
  const [searchText, setSearchText] = useState('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [dateRange, setDateRange] = useState<any>(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentProcess, setCurrentProcess] = useState<ProcessItem | null>(null);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [processData, setProcessData] = useState<ProcessItem[]>([]);

  // 获取流程列表数据
  const fetchProcessList = async () => {
    try {
      setLoading(true);
      console.log('Fetching process list...');

      // 导入apiClient
      const apiClient = (await import('../../services/apiClient')).default;

      // 使用apiClient替代fetch
      const response = await apiClient.get('/processes/my-processes');

      console.log('Process list data:', response.data);
      setProcessData(response.data);
    } catch (error) {
      console.error('获取流程列表出错:', error);
      message.error(error instanceof Error ? error.message : '获取流程列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 组件加载时获取数据
  useEffect(() => {
    fetchProcessList();
  }, []);

  // 过滤数据
  const getFilteredData = () => {
    let filteredData = [...processData];

    // 根据标签页筛选
    if (activeTab === 'draft') {
      filteredData = filteredData.filter(item => item.status === 'DRAFT');
    } else if (activeTab === 'processing') {
      filteredData = filteredData.filter(item => item.status === 'PENDING');
    } else if (activeTab === 'completed') {
      filteredData = filteredData.filter(item => ['APPROVED', 'REJECTED'].includes(item.status));
    }

    // 根据搜索文本筛选
    if (searchText) {
      filteredData = filteredData.filter(
        item => item.title.includes(searchText) ||
               item.id.includes(searchText) ||
               item.applicant.includes(searchText)
      );
    }

    // 根据类型筛选
    if (selectedType) {
      filteredData = filteredData.filter(item => item.type === selectedType);
    }

    // 根据状态筛选
    if (selectedStatus) {
      filteredData = filteredData.filter(item => item.status === selectedStatus);
    }

    // 根据日期范围筛选
    if (dateRange) {
      const [startDate, endDate] = dateRange;
      filteredData = filteredData.filter(item => {
        const itemDate = new Date(item.created_at);
        return itemDate >= startDate.startOf('day').toDate() &&
               itemDate <= endDate.endOf('day').toDate();
      });
    }

    return filteredData;
  };

  // 查看流程详情
  const handleViewDetail = (record: ProcessItem) => {
    setCurrentProcess(record);
    setDetailVisible(true);
  };

  // 查看流程历史
  const handleViewHistory = (record: ProcessItem) => {
    setCurrentProcess(record);
    setHistoryVisible(true);
  };

  // 删除流程
  const handleDelete = async (record: ProcessItem) => {
    Modal.confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除流程\"${record.title}\"吗？`,
      okText: '确认',
      cancelText: '取消',
      async onOk() {
        try {
          // 导入apiClient
          const apiClient = (await import('../../services/apiClient')).default;

          // 使用apiClient替代fetch
          await apiClient.delete(`/processes/${record.id}`);

          message.success('删除成功');
          fetchProcessList(); // 重新获取列表
        } catch (error) {
          console.error('删除流程出错:', error);
          message.error('删除失败，请稍后重试');
        }
      }
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '流程编号',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (text: string) => <a>#{text}</a>
    },
    {
      title: '流程标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      render: (text: string) => (
        <div style={{
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          maxWidth: '200px'
        }}>
          {text}
        </div>
      )
    },
    {
      title: '流程类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (text: string) => {
        const type = PROCESS_TYPES[text] || { text: '其他', color: 'blue' };
        return <Tag color={type.color}>{type.text}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (text: string) => {
        const status = STATUS_MAP[text] || { text: text, color: 'default' };
        let icon = null;

        if (text === 'DRAFT') {
          icon = <EditOutlined />;
        } else if (text === 'PENDING') {
          icon = <ClockCircleOutlined />;
        } else if (text === 'APPROVED') {
          icon = <CheckCircleOutlined />;
        } else if (text === 'REJECTED') {
          icon = <CloseCircleOutlined />;
        }

        return (
          <Space>
            {icon}
            <Badge status={status.color as any} text={status.text} />
          </Space>
        );
      }
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 90,
      render: (text: string) => {
        const priority = PRIORITY_MAP[text] || { text: '普通', color: 'blue' };
        return <Tag color={priority.color}>{priority.text}</Tag>;
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: string) => formatDate(text)
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 180,
      render: (text: string) => formatDate(text)
    },
    {
      title: '进度',
      key: 'progress',
      width: 150,
      render: (_: any, record: ProcessItem) => (
        <div className="process-progress">
          {record.currentStep !== undefined && record.totalSteps !== undefined ? (
            <Tooltip title={`${record.currentStep}/${record.totalSteps} 步骤`}>
              <Progress
                percent={Math.round((record.currentStep / record.totalSteps) * 100)}
                size="small"
                status={record.status === 'REJECTED' ? 'exception' : 'active'}
              />
            </Tooltip>
          ) : '-'}
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_: React.ReactNode, record: ProcessItem) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {record.status === 'DRAFT' && (
            <Tooltip title="编辑">
              <Button
                type="link"
                icon={<EditOutlined />}
              />
            </Tooltip>
          )}
          <Tooltip title="流程历史">
            <Button
              type="link"
              icon={<HistoryOutlined />}
              onClick={() => handleViewHistory(record)}
            />
          </Tooltip>
          {['DRAFT', 'APPROVED', 'REJECTED'].includes(record.status) && (
            <Tooltip title="删除">
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(record)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  // 渲染流程详情
  const renderProcessDetail = () => {
    if (!currentProcess) return null;

    return (
      <div className="process-detail">
        <div className="detail-header">
          <h2>{currentProcess.title}</h2>
          <div className="process-meta">
            <div className="meta-item">
              <span className="meta-label">流程编号：</span>
              <span className="meta-value">{currentProcess.id}</span>
            </div>
            <div className="meta-item">
              <span className="meta-label">创建时间：</span>
              <span className="meta-value">{currentProcess.created_at}</span>
            </div>
            <div className="meta-item">
              <span className="meta-label">更新时间：</span>
              <span className="meta-value">{currentProcess.updated_at}</span>
            </div>
          </div>
        </div>

        <div className="detail-content">
          <div className="detail-section">
            <h3>基本信息</h3>
            <div className="detail-row">
              <div className="detail-col">
                <span className="detail-label">流程类型：</span>
                <span className="detail-value">
                  <Tag color="blue">{currentProcess.type}</Tag>
                </span>
              </div>
              <div className="detail-col">
                <span className="detail-label">优先级：</span>
                <span className="detail-value">
                  <Tag color={currentProcess.priority === '紧急' ? 'red' : 'blue'}>
                    {currentProcess.priority}
                  </Tag>
                </span>
              </div>
            </div>
            <div className="detail-row">
              <div className="detail-col">
                <span className="detail-label">申请人：</span>
                <span className="detail-value">{currentProcess.applicant}</span>
              </div>
              <div className="detail-col">
                <span className="detail-label">所属部门：</span>
                <span className="detail-value">{currentProcess.department}</span>
              </div>
            </div>
          </div>

          <div className="detail-section">
            <h3>流程状态</h3>
            <Steps current={currentProcess.currentStep} className="process-steps">
              <Step title="发起申请" description="2025-03-12 10:30:45" />
              <Step title="部门经理审批" description="2025-03-13 14:20:33" />
              <Step title="财务审批" description={currentProcess.status === 'PENDING' ? '处理中' : '2025-03-14 09:15:22'} />
            </Steps>
          </div>

          <div className="detail-section">
            <h3>申请内容</h3>
            <div className="detail-content-text">
              {currentProcess.type === '报销申请' && (
                <div className="expense-detail">
                  <div className="expense-row">
                    <span className="expense-label">报销事由：</span>
                    <span className="expense-value">上海技术交流会相关费用</span>
                  </div>
                  <div className="expense-row">
                    <span className="expense-label">出差地点：</span>
                    <span className="expense-value">上海</span>
                  </div>
                  <div className="expense-row">
                    <span className="expense-label">出差日期：</span>
                    <span className="expense-value">2025-03-05 至 2025-03-08</span>
                  </div>
                  <div className="expense-row">
                    <span className="expense-label">报销明细：</span>
                  </div>
                  <div className="expense-table">
                    <table>
                      <thead>
                        <tr>
                          <th>费用类型</th>
                          <th>金额（元）</th>
                          <th>说明</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>交通费</td>
                          <td>3200</td>
                          <td>往返机票</td>
                        </tr>
                        <tr>
                          <td>住宿费</td>
                          <td>1800</td>
                          <td>3晚酒店</td>
                        </tr>
                        <tr>
                          <td>餐饮费</td>
                          <td>600</td>
                          <td>3天餐饮</td>
                        </tr>
                        <tr>
                          <td>市内交通</td>
                          <td>200</td>
                          <td>打车费</td>
                        </tr>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colSpan={3}>
                            <div className="expense-total">
                              <span>合计金额：</span>
                              <span className="total-amount">5800 元</span>
                            </div>
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </div>
              )}

              {currentProcess.type === '休假申请' && (
                <div className="leave-detail">
                  <div className="leave-row">
                    <span className="leave-label">休假类型：</span>
                    <span className="leave-value">年假</span>
                  </div>
                  <div className="leave-row">
                    <span className="leave-label">休假日期：</span>
                    <span className="leave-value">2025-03-15 至 2025-03-18</span>
                  </div>
                  <div className="leave-row">
                    <span className="leave-label">休假天数：</span>
                    <span className="leave-value">4天</span>
                  </div>
                  <div className="leave-row">
                    <span className="leave-label">休假事由：</span>
                    <span className="leave-value">个人事务处理</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="detail-section">
            <h3>审批意见</h3>
            <div className="approval-comments">
              <div className="comment-item">
                <div className="comment-header">
                  <span className="comment-user"><UserOutlined /> 李经理（部门经理）</span>
                  <span className="comment-time">2025-03-13 14:20:33</span>
                </div>
                <div className="comment-content">
                  <Tag color="green">同意</Tag>
                  <span className="comment-text">费用合理，同意报销。</span>
                </div>
              </div>

              {currentProcess.status === 'APPROVED' && (
                <div className="comment-item">
                  <div className="comment-header">
                    <span className="comment-user"><UserOutlined /> 王财务（财务部）</span>
                    <span className="comment-time">2025-03-14 09:15:22</span>
                  </div>
                  <div className="comment-content">
                    <Tag color="green">同意</Tag>
                    <span className="comment-text">费用明细清晰，票据齐全，同意报销。</span>
                  </div>
                </div>
              )}

              {currentProcess.status === 'REJECTED' && (
                <div className="comment-item">
                  <div className="comment-header">
                    <span className="comment-user"><UserOutlined /> 王财务（财务部）</span>
                    <span className="comment-time">2025-03-11 16:30:45</span>
                  </div>
                  <div className="comment-content">
                    <Tag color="red">拒绝</Tag>
                    <span className="comment-text">预算超出部门季度限额，请调整后重新提交。</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 渲染流程历史
  const renderProcessHistory = () => {
    if (!currentProcess) return null;

    return (
      <div className="process-history">
        <h2>流程历史记录</h2>
        <Timeline className="history-timeline">
          <Timeline.Item dot={<FileTextOutlined style={{ fontSize: '16px' }} />}>
            <div className="timeline-content">
              <div className="timeline-title">发起申请</div>
              <div className="timeline-time">2025-03-12 10:30:45</div>
              <div className="timeline-user">张三（技术部）</div>
              <div className="timeline-detail">创建了"{currentProcess.title}"流程申请</div>
            </div>
          </Timeline.Item>

          <Timeline.Item dot={<CommentOutlined style={{ fontSize: '16px' }} />}>
            <div className="timeline-content">
              <div className="timeline-title">部门经理审批</div>
              <div className="timeline-time">2025-03-13 14:20:33</div>
              <div className="timeline-user">李经理（技术部）</div>
              <div className="timeline-detail">
                <Tag color="green">同意</Tag>
                <span>费用合理，同意报销。</span>
              </div>
            </div>
          </Timeline.Item>

          {currentProcess.status !== 'PENDING' && (
            <Timeline.Item
              dot={
                currentProcess.status === 'APPROVED'
                  ? <CheckCircleOutlined style={{ fontSize: '16px', color: '#52c41a' }} />
                  : <CloseCircleOutlined style={{ fontSize: '16px', color: '#f5222d' }} />
              }
            >
              <div className="timeline-content">
                <div className="timeline-title">财务审批</div>
                <div className="timeline-time">
                  {currentProcess.status === 'APPROVED' ? '2025-03-14 09:15:22' : '2025-03-11 16:30:45'}
                </div>
                <div className="timeline-user">王财务（财务部）</div>
                <div className="timeline-detail">
                  {currentProcess.status === 'APPROVED' ? (
                    <>
                      <Tag color="green">同意</Tag>
                      <span>费用明细清晰，票据齐全，同意报销。</span>
                    </>
                  ) : (
                    <>
                      <Tag color="red">拒绝</Tag>
                      <span>预算超出部门季度限额，请调整后重新提交。</span>
                    </>
                  )}
                </div>
              </div>
            </Timeline.Item>
          )}

          {currentProcess.status === 'APPROVED' && (
            <Timeline.Item dot={<CheckCircleOutlined style={{ fontSize: '16px', color: '#52c41a' }} />}>
              <div className="timeline-content">
                <div className="timeline-title">流程完成</div>
                <div className="timeline-time">2025-03-14 09:20:15</div>
                <div className="timeline-detail">流程审批通过，完成处理</div>
              </div>
            </Timeline.Item>
          )}
        </Timeline>
      </div>
    );
  };

  return (
    <div className="my-process-container">
      <div className="page-header">
        <h1>我的流程</h1>
        <p className="page-description">
          管理您发起的所有流程申请，包括草稿、审批中和已完成的流程
        </p>
      </div>

      <Card className="process-card">
        <div className="filter-bar">
          <div className="search-box">
            <Input
              placeholder="搜索流程标题、编号或申请人"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              allowClear
              style={{ width: 250 }}
            />
          </div>

          <div className="filter-options">
            <Select
              placeholder="流程类型"
              style={{ width: 120 }}
              value={selectedType}
              onChange={value => setSelectedType(value)}
              allowClear
            >
              {Object.entries(PROCESS_TYPES).map(([key, value]) => (
                <Option key={key} value={key}>{value.text}</Option>
              ))}
            </Select>

            <Select
              placeholder="流程状态"
              style={{ width: 120 }}
              value={selectedStatus}
              onChange={value => setSelectedStatus(value)}
              allowClear
            >
              {Object.entries(STATUS_MAP).map(([key, value]) => (
                <Option key={key} value={key}>{value.text}</Option>
              ))}
            </Select>

            <RangePicker
              placeholder={['开始日期', '结束日期']}
              onChange={value => setDateRange(value)}
              style={{ width: 250 }}
            />
          </div>

          <div className="action-buttons">
            <Button type="primary" icon={<PlusOutlined />}>
              发起流程
            </Button>
          </div>
        </div>

        <Tabs
          activeKey={activeTab}
          onChange={key => setActiveTab(key)}
          className="process-tabs"
        >
          <TabPane
            tab={
              <span>
                全部流程
                <Badge count={processData.length} style={{ marginLeft: 8 }} />
              </span>
            }
            key="all"
          />
          <TabPane
            tab={
              <span>
                草稿
                <Badge
                  count={processData.filter(item => item.status === 'DRAFT').length}
                  style={{ marginLeft: 8 }}
                />
              </span>
            }
            key="draft"
          />
          <TabPane
            tab={
              <span>
                审批中
                <Badge
                  count={processData.filter(item => item.status === 'PENDING').length}
                  style={{ marginLeft: 8 }}
                  color="processing"
                />
              </span>
            }
            key="processing"
          />
          <TabPane
            tab={
              <span>
                已完成
                <Badge
                  count={processData.filter(item => ['APPROVED', 'REJECTED'].includes(item.status)).length}
                  style={{ marginLeft: 8 }}
                />
              </span>
            }
            key="completed"
          />
        </Tabs>

        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={getFilteredData()}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showTotal: total => `共 ${total} 条记录`,
              showQuickJumper: true,
              showSizeChanger: true,
              pageSizeOptions: ['10', '20', '50', '100']
            }}
            scroll={{ x: 1500 }}
            className="process-table"
          />
        </Spin>
      </Card>

      <Modal
        title="流程详情"
        visible={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
        bodyStyle={{ maxHeight: '70vh', overflow: 'auto' }}
      >
        {renderProcessDetail()}
      </Modal>

      <Modal
        title="流程历史"
        visible={historyVisible}
        onCancel={() => setHistoryVisible(false)}
        footer={[
          <Button key="close" onClick={() => setHistoryVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {renderProcessHistory()}
      </Modal>
    </div>
  );
};

export default MyProcess;
