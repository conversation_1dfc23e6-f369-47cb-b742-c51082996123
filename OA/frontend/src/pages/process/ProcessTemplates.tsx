import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Button,
  Input,
  Select,
  Tag,
  Space,
  Tooltip,
  Modal,
  Switch,
  Form,
  Radio,
  Tabs,
  Empty,
  message
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  FileTextOutlined,
  StarOutlined,
  StarFilled,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  PlusCircleOutlined,
  MinusCircleOutlined
} from '@ant-design/icons';

import '../../styles/pages/process/ProcessTemplates.less';

const { Option } = Select;
const { TabPane } = Tabs;

interface ApprovalNode {
  id: string;
  type: 'role' | 'user';
  approver: string;
  description?: string;
  conditions?: {
    field: string;
    operator: 'equals' | 'greater' | 'less' | 'contains';
    value: any;
  }[];
}

interface FormField {
  id: string;
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'number' | 'date' | 'dateRange' | 'select' | 'radio' | 'checkbox' | 'upload' | 'table';
  required: boolean;
  placeholder?: string;
  options?: { label: string; value: any }[];
  defaultValue?: any;
  rules?: any[];
  props?: Record<string, any>;
}

interface ProcessTemplate {
  id: string;
  name: string;  // 后端返回的是name，前端显示为title
  title?: string;  // 兼容前端显示
  type: string;
  department: string;
  description: string;
  creator: string;
  created_at: string;  // 后端返回的创建时间
  updated_at: string;  // 后端返回的更新时间
  createTime?: string;  // 兼容前端显示
  updateTime?: string;  // 兼容前端显示
  usage_count: number;
  usageCount?: number;  // 兼容前端显示
  version: string;
  isFavorite: boolean;
  status: string;
  steps: {
    approvalFlow: ApprovalNode[];
  };
  form_schema: {
    formFields: FormField[];
  };
  // 兼容前端显示
  approvalFlow?: ApprovalNode[];
  formFields?: FormField[];
}

const ProcessTemplates: React.FC = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [searchText, setSearchText] = useState('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState<ProcessTemplate | null>(null);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 添加模板数据状态
  const [templateData, setTemplateData] = useState<ProcessTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 从后端获取流程模板数据
  useEffect(() => {
    fetchTemplates();
  }, []);

  // 处理后端返回的数据，转换为前端需要的格式
  const processTemplateData = (data: any[]): ProcessTemplate[] => {
    return data.map(item => {
      // 处理steps和form_schema数据
      let stepsData: any = {};
      let formSchemaData: any = {};

      try {
        // 处理steps数据（审批流程）
        stepsData = typeof item.steps === 'string' ? JSON.parse(item.steps) : item.steps || {};
      } catch (e) {
        console.error('解析steps数据失败:', e);
        stepsData = {};
      }

      try {
        // 处理form_schema数据（表单字段）
        formSchemaData = typeof item.form_schema === 'string' ? JSON.parse(item.form_schema) : item.form_schema || {};
      } catch (e) {
        console.error('解析form_schema数据失败:', e);
        formSchemaData = {};
      }

      return {
        id: `TPL${item.id.toString().padStart(3, '0')}`,
        name: item.name,
        title: item.name, // 使用name作为title显示
        description: item.description || '',
        // 优先使用独立字段，如果没有则从steps中获取
        type: item.type || stepsData.type || '未分类',
        department: item.department || stepsData.department || '全公司',
        creator: item.creator?.full_name || item.creator?.username || '系统管理员',
        created_at: item.created_at,
        updated_at: item.updated_at,
        createTime: new Date(item.created_at).toLocaleString(),
        updateTime: item.updated_at ? new Date(item.updated_at).toLocaleString() : new Date(item.created_at).toLocaleString(),
        usage_count: item.usage_count || 0,
        usageCount: item.usage_count || 0,
        version: item.version || '1.0',
        isFavorite: false, // 后端暂无此数据
        status: item.status || (item.is_active ? '启用' : '禁用'),
        // 保存原始数据结构
        steps: stepsData,
        form_schema: formSchemaData,
        // 兼容前端显示
        approvalFlow: stepsData.approvalFlow || [],
        formFields: formSchemaData.formFields || []
      };
    });
  };

  const fetchTemplates = async () => {
    setIsLoading(true);
    try {
      // 导入apiClient
      const apiClient = (await import('../../services/apiClient')).default;

      // 使用apiClient替代fetch
      const response = await apiClient.get('/processes/templates');

      console.log('获取到的模板数据:', response.data);

      // 使用processTemplateData函数处理数据
      const formattedData = processTemplateData(response.data);
      console.log('处理后的模板数据:', formattedData);
      setTemplateData(formattedData);
    } catch (error) {
      console.error('获取流程模板错误:', error);
      message.error('获取流程模板失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 模板数据示例（当API获取失败时使用）
  // 注意：此数据仅作为备用，当API获取失败时可以手动设置
  // 如果API请求失败，可以取消注释下面这行代码：
  // setTemplateData([
  /*
    {
      id: 'TPL001',
      title: '差旅报销申请',
      type: '报销申请',
      department: '全公司',
      description: '用于员工出差后的费用报销，包括交通、住宿、餐饮等费用',
      creator: '系统管理员',
      createTime: '2025-01-10 10:30:45',
      updateTime: '2025-02-15 14:22:10',
      usageCount: 156,
      isFavorite: true,
      status: '启用',
      approvalFlow: [
        {
          id: 'node1',
          type: 'role',
          approver: '部门经理',
          description: '部门初审'
        },
        {
          id: 'node2',
          type: 'role',
          approver: '财务经理',
          description: '财务审核',
          conditions: [
            {
              field: 'totalAmount',
              operator: 'greater',
              value: 5000
            }
          ]
        }
      ],
      formFields: [
        { id: 'field1', name: 'tripPurpose', label: '出差目的', type: 'text', required: true },
        { id: 'field2', name: 'destination', label: '目的地', type: 'text', required: true },
        { id: 'field3', name: 'dateRange', label: '出差日期', type: 'dateRange', required: true },
        { id: 'field4', name: 'expenseItems', label: '费用明细', type: 'table', required: true },
        { id: 'field5', name: 'totalAmount', label: '总金额', type: 'number', required: true },
        { id: 'field6', name: 'attachments', label: '附件', type: 'upload', required: true },
        { id: 'field7', name: 'notes', label: '备注', type: 'textarea', required: false }
      ]
    },
    {
      id: 'TPL002',
      title: '年假申请',
      type: '休假申请',
      department: '全公司',
      description: '用于员工申请年假，需提前3天提交申请',
      creator: '系统管理员',
      createTime: '2025-01-15 09:45:12',
      updateTime: '2025-01-15 09:45:12',
      usageCount: 238,
      isFavorite: true,
      status: '启用',
      approvalFlow: [
        {
          id: 'node1',
          type: 'role',
          approver: '部门经理',
          description: '部门审批'
        },
        {
          id: 'node2',
          type: 'role',
          approver: '人事主管',
          description: '人事审核',
          conditions: [
            {
              field: 'days',
              operator: 'greater',
              value: 3
            }
          ]
        }
      ],
      formFields: [
        { id: 'field1', name: 'leaveType', label: '休假类型', type: 'select', required: true, options: [
          { label: '年假', value: 'annual' },
          { label: '病假', value: 'sick' },
          { label: '事假', value: 'personal' }
        ] },
        { id: 'field2', name: 'dateRange', label: '休假日期', type: 'dateRange', required: true },
        { id: 'field3', name: 'days', label: '休假天数', type: 'number', required: true },
        { id: 'field4', name: 'reason', label: '休假事由', type: 'textarea', required: true },
        { id: 'field5', name: 'contact', label: '紧急联系方式', type: 'text', required: true },
        { id: 'field6', name: 'handover', label: '工作交接人', type: 'select', required: true }
      ]
    },
    {
      id: 'TPL003',
      title: '办公用品采购申请',
      type: '采购申请',
      department: '行政部',
      description: '用于申请采购办公用品，包括文具、设备等',
      creator: '李行政',
      createTime: '2025-02-01 15:20:30',
      updateTime: '2025-02-01 15:20:30',
      usageCount: 42,
      isFavorite: false,
      status: '启用',
      approvalFlow: [
        {
          id: 'node1',
          type: 'role',
          approver: '行政经理',
          description: '行政审核'
        },
        {
          id: 'node2',
          type: 'role',
          approver: '财务经理',
          description: '财务审批',
          conditions: [
            {
              field: 'totalAmount',
              operator: 'greater',
              value: 1000
            }
          ]
        }
      ],
      formFields: [
        { id: 'field1', name: 'itemList', label: '采购清单', type: 'table', required: true },
        { id: 'field2', name: 'totalAmount', label: '总金额', type: 'number', required: true },
        { id: 'field3', name: 'purpose', label: '用途说明', type: 'textarea', required: true },
        { id: 'field4', name: 'expectedDelivery', label: '期望交付日期', type: 'date', required: true },
        { id: 'field5', name: 'supplier', label: '建议供应商', type: 'text', required: false }
      ]
    },
    {
      id: 'TPL004',
      title: '项目立项申请',
      type: '项目申请',
      department: '技术部',
      description: '用于新项目的立项申请，包括项目计划、资源需求等',
      creator: '张技术',
      createTime: '2025-02-10 11:10:25',
      updateTime: '2025-03-01 09:45:12',
      usageCount: 18,
      isFavorite: false,
      status: '启用',
      approvalFlow: [
        {
          id: 'node1',
          type: 'role',
          approver: '技术总监',
          description: '技术可行性评估'
        },
        {
          id: 'node2',
          type: 'role',
          approver: '产品总监',
          description: '产品价值评估'
        },
        {
          id: 'node3',
          type: 'role',
          approver: '总经理',
          description: '最终审批',
          conditions: [
            {
              field: 'budget',
              operator: 'greater',
              value: 100000
            }
          ]
        }
      ],
      formFields: [
        { id: 'field1', name: 'projectName', label: '项目名称', type: 'text', required: true },
        { id: 'field2', name: 'projectType', label: '项目类型', type: 'select', required: true, options: [
          { label: '研发项目', value: 'rd' },
          { label: '基础设施', value: 'infra' },
          { label: '业务系统', value: 'business' }
        ] },
        { id: 'field3', name: 'objectives', label: '项目目标', type: 'textarea', required: true },
        { id: 'field4', name: 'schedule', label: '项目计划', type: 'table', required: true },
        { id: 'field5', name: 'resources', label: '资源需求', type: 'table', required: true },
        { id: 'field6', name: 'budget', label: '预算', type: 'number', required: true },
        { id: 'field7', name: 'risks', label: '风险评估', type: 'textarea', required: true },
        { id: 'field8', name: 'attachments', label: '附件', type: 'upload', required: false }
      ]
    },
    {
      id: 'TPL005',
      title: '加班申请',
      type: '工时申请',
      department: '全公司',
      description: '用于员工申请加班，包括加班时间、原因等',
      creator: '系统管理员',
      createTime: '2025-01-20 14:30:20',
      updateTime: '2025-01-20 14:30:20',
      usageCount: 95,
      isFavorite: false,
      status: '启用',
      approvalFlow: [
        {
          id: 'node1',
          type: 'role',
          approver: '部门经理',
          description: '加班审批',
          conditions: [
            {
              field: 'hours',
              operator: 'greater',
              value: 4
            }
          ]
        }
      ],
      formFields: [
        { id: 'field1', name: 'overtimeDate', label: '加班日期', type: 'date', required: true },
        { id: 'field2', name: 'timeRange', label: '加班时间段', type: 'date', required: true },
        { id: 'field3', name: 'hours', label: '加班小时数', type: 'number', required: true },
        { id: 'field4', name: 'reason', label: '加班原因', type: 'textarea', required: true },
        { id: 'field5', name: 'compensationType', label: '补偿方式', type: 'radio', required: true, options: [
          { label: '调休', value: 'leave' },
          { label: '加班费', value: 'payment' }
        ] }
      ]
    }
  */
  // ]);

  // 过滤数据
  const getFilteredData = () => {
    let filteredData = [...templateData];

    // 根据标签页筛选
    if (activeTab === 'favorite') {
      filteredData = filteredData.filter(item => item.isFavorite);
    } else if (activeTab === 'created') {
      // 获取当前用户信息
      const userStr = localStorage.getItem('user');
      let currentUser = '';
      if (userStr) {
        try {
          const userData = JSON.parse(userStr);
          currentUser = userData.full_name || userData.username || '';
        } catch (e) {
          console.error('解析用户数据失败:', e);
        }
      }
      filteredData = filteredData.filter(item => item.creator === currentUser);
    }

    // 根据搜索文本筛选
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filteredData = filteredData.filter(
        item => {
          const titleMatch = (item.title || '').toLowerCase().includes(searchLower);
          const nameMatch = (item.name || '').toLowerCase().includes(searchLower);
          const descMatch = (item.description || '').toLowerCase().includes(searchLower);
          const typeMatch = (item.type || '').toLowerCase().includes(searchLower);
          const deptMatch = (item.department || '').toLowerCase().includes(searchLower);

          return titleMatch || nameMatch || descMatch || typeMatch || deptMatch;
        }
      );
    }

    // 根据类型筛选
    if (selectedType) {
      filteredData = filteredData.filter(item => item.type === selectedType);
    }

    // 根据部门筛选
    if (selectedDepartment) {
      filteredData = filteredData.filter(item => item.department === selectedDepartment);
    }

    return filteredData;
  };

  // 查看模板详情
  const handleViewDetail = (template: ProcessTemplate) => {
    setCurrentTemplate(template);
    setDetailVisible(true);
  };

  // 切换收藏状态
  const toggleFavorite = (template: ProcessTemplate, e: React.MouseEvent) => {
    e.stopPropagation();

    // 更新本地状态
    const newData = templateData.map(item => {
      if (item.id === template.id) {
        return { ...item, isFavorite: !item.isFavorite };
      }
      return item;
    });
    setTemplateData(newData);

    // 实际项目中需要调用API更新收藏状态
    message.success(template.isFavorite ? '已取消收藏' : '已添加收藏');
  };

  // 删除模板
  const handleDelete = (template: ProcessTemplate, e: React.MouseEvent) => {
    e.stopPropagation();
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除模板"${template.title}"吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk() {
        // 实际项目中需要调用API删除模板
        // 临时从本地数据中移除
        const newData = templateData.filter(item => item.id !== template.id);
        setTemplateData(newData);
        message.success('模板已删除');
      }
    });
  };

  // 复制模板
  const handleCopy = (template: ProcessTemplate, e: React.MouseEvent) => {
    e.stopPropagation();
    // 实际项目中需要调用API复制模板
    // 临时在本地数据中添加一个复制版本
    const copiedTemplate = {
      ...template,
      id: `${template.id}_copy`,
      title: `${template.title} (复制)`,
      isFavorite: false,
      createTime: new Date().toLocaleString(),
      updateTime: new Date().toLocaleString()
    };
    setTemplateData([...templateData, copiedTemplate]);
    message.success(`已复制模板"${template.title}"`);
  };

  // 使用模板创建流程
  const handleUseTemplate = (template: ProcessTemplate) => {
    message.success(`正在使用"${template.title}"模板创建新流程`);
    // 实际项目中需要跳转到流程创建页面
  };

  // 编辑模板
  const handleEditTemplate = (template: ProcessTemplate) => {
    setCurrentTemplate(template);

    // 确保我们有正确的审批流程和表单字段数据
    let approvalFlowData: ApprovalNode[] = [];
    let formFieldsData: FormField[] = [];

    // 处理审批流程数据
    if (Array.isArray(template.approvalFlow) && template.approvalFlow.length > 0) {
      approvalFlowData = template.approvalFlow;
    } else if (template.steps && Array.isArray(template.steps.approvalFlow)) {
      approvalFlowData = template.steps.approvalFlow;
    }

    // 处理表单字段数据
    if (Array.isArray(template.formFields) && template.formFields.length > 0) {
      formFieldsData = template.formFields;
    } else if (template.form_schema && Array.isArray(template.form_schema.formFields)) {
      formFieldsData = template.form_schema.formFields;
    }

    // 设置表单值
    form.setFieldsValue({
      title: template.title || template.name,
      type: template.type,
      department: template.department,
      description: template.description,
      status: template.status,
      version: template.version || '1.0',
      approvalFlow: approvalFlowData,
      formFields: formFieldsData
    });

    setEditModalVisible(true);
  };

  // 保存模板修改
  const handleSaveTemplate = async (values: ProcessTemplate) => {
    try {
      // 实际项目中需要调用API更新模板
      console.log('保存模板修改:', values);
      message.success('模板更新成功');
      setEditModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('保存失败，请重试');
    }
  };

  // 创建新模板
  const handleCreateTemplate = async (values: ProcessTemplate) => {
    try {
      // 实际项目中需要调用API创建模板
      console.log('创建新模板:', values);
      message.success('模板创建成功');
      setCreateModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('创建失败，请重试');
    }
  };

  // 渲染审批节点
  const renderApprovalNode = (node: ApprovalNode, index: number) => {
    return (
      <div className="approval-node" key={node.id}>
        <div className="node-header">
          <div className="node-order">{index + 1}</div>
          <div className="node-title">
            <div className="node-role">{node.approver}</div>
            <div className="node-desc">{node.description}</div>
          </div>
        </div>
        {node.conditions && node.conditions.length > 0 && (
          <div className="node-conditions">
            {node.conditions.map((condition, idx) => (
              <Tag key={idx} color="blue">
                {`当 ${condition.field} ${condition.operator === 'greater' ? '大于' :
                  condition.operator === 'less' ? '小于' :
                  condition.operator === 'equals' ? '等于' : '包含'} ${condition.value}`}
              </Tag>
            ))}
          </div>
        )}
      </div>
    );
  };

  // 渲染表单字段
  const renderFormField = (field: FormField) => {
    return (
      <div className="field-card" key={field.id}>
        <div className="field-header">
          <div className="field-name">
            {field.label}
            {field.required && <span className="required">*</span>}
          </div>
          <Tag color="processing">{field.type}</Tag>
        </div>
        <div className="field-content">
          {field.options && (
            <div className="field-options">
              {field.options.map((opt, idx) => (
                <Tag key={idx}>{opt.label}</Tag>
              ))}
            </div>
          )}
          {field.placeholder && (
            <div className="field-placeholder">
              提示：{field.placeholder}
            </div>
          )}
        </div>
      </div>
    );
  };

  // 渲染模板详情
  const renderTemplateDetail = () => {
    if (!currentTemplate) return null;

    return (
      <div className="template-detail">
        <div className="detail-header">
          <h2>{currentTemplate.title || currentTemplate.name}</h2>
          <div className="template-meta">
            <Tag color="blue">{currentTemplate.type}</Tag>
            <span className="meta-item">
              <TeamOutlined /> {currentTemplate.department}
            </span>
            <span className="meta-item">
              <ClockCircleOutlined /> 创建于 {currentTemplate.createTime}
            </span>
            <span className="meta-item">
              <CheckCircleOutlined /> 使用次数: {currentTemplate.usageCount || currentTemplate.usage_count}
            </span>
            {currentTemplate.version && (
              <span className="meta-item">
                <Tag color="green">版本 {currentTemplate.version}</Tag>
              </span>
            )}
          </div>
        </div>

        <div className="detail-content">
          <div className="detail-section">
            <h3>模板描述</h3>
            <p>{currentTemplate.description}</p>
          </div>

          <div className="detail-section">
            <h3>审批流程</h3>
            <div className="approval-flow">
              {(currentTemplate.approvalFlow || currentTemplate.steps?.approvalFlow || []).map((node, index) =>
                renderApprovalNode(node, index)
              )}
            </div>
          </div>

          <div className="detail-section">
            <h3>表单字段</h3>
            <div className="form-fields-grid">
              {(currentTemplate.formFields || currentTemplate.form_schema?.formFields || []).map(field =>
                renderFormField(field)
              )}
            </div>
          </div>
        </div>

        <div className="detail-actions">
          <Space>
            <Button type="primary" onClick={() => handleUseTemplate(currentTemplate)}>
              使用此模板
            </Button>
            <Button type="default" onClick={() => handleEditTemplate(currentTemplate)}>
              编辑模板
            </Button>
            <Button onClick={() => setDetailVisible(false)}>
              关闭
            </Button>
          </Space>
        </div>
      </div>
    );
  };

  return (
    <div className="process-templates-container">
      <div className="page-header">
        <h1>流程模板</h1>
        <p className="page-description">
          查看和管理流程模板，快速创建标准化的流程申请
        </p>
      </div>

      <Card className="templates-card">
        <div className="filter-bar">
          <div className="search-box">
            <Input
              placeholder="搜索模板名称或描述"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              allowClear
            />
          </div>

          <div className="filter-options">
            <Select
              placeholder="流程类型"
              style={{ width: 120 }}
              value={selectedType}
              onChange={value => setSelectedType(value)}
              allowClear
            >
              {Array.from(new Set(templateData.map(item => item.type))).map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>

            <Select
              placeholder="所属部门"
              style={{ width: 120 }}
              value={selectedDepartment}
              onChange={value => setSelectedDepartment(value)}
              allowClear
            >
              {Array.from(new Set(templateData.map(item => item.department))).map(dept => (
                <Option key={dept} value={dept}>{dept}</Option>
              ))}
            </Select>

            <Button
              icon={<SearchOutlined />}
              onClick={() => fetchTemplates()}
              loading={isLoading}
              style={{ marginLeft: 8 }}
            >
              刷新
            </Button>
          </div>

          <div className="action-buttons">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              创建模板
            </Button>
          </div>
        </div>

        <Tabs
          activeKey={activeTab}
          onChange={key => setActiveTab(key)}
          className="templates-tabs"
        >
          <TabPane tab="全部模板" key="all" />
          <TabPane tab="我的收藏" key="favorite" />
          <TabPane tab="我创建的" key="created" />
        </Tabs>

        <List
          grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 4 }}
          dataSource={getFilteredData()}
          loading={isLoading}
          locale={{ emptyText: <Empty description="暂无模板数据" /> }}
          renderItem={item => (
            <List.Item>
              <Card
                className="template-card"
                hoverable
                onClick={() => handleViewDetail(item)}
              >
                <div className="template-icon">
                  <FileTextOutlined />
                </div>
                <div className="template-header">
                  <div className="template-title">{item.title}</div>
                  <div
                    className="favorite-icon"
                    onClick={(e) => toggleFavorite(item, e)}
                  >
                    {item.isFavorite ?
                      <StarFilled style={{ color: '#faad14' }} /> :
                      <StarOutlined />
                    }
                  </div>
                </div>
                <div className="template-type">
                  <Tag color="blue">{item.type}</Tag>
                  <Tag>{item.department}</Tag>
                </div>
                <div className="template-description">{item.description}</div>
                <div className="template-meta">
                  <div className="meta-item">
                    <ClockCircleOutlined /> 使用: {item.usageCount}次
                  </div>
                </div>
                <div className="template-actions">
                  <Space>
                    <Tooltip title="查看详情">
                      <Button
                        type="text"
                        icon={<EyeOutlined />}
                        size="small"
                      />
                    </Tooltip>
                    <Tooltip title="复制模板">
                      <Button
                        type="text"
                        icon={<CopyOutlined />}
                        size="small"
                        onClick={(e) => handleCopy(item, e)}
                      />
                    </Tooltip>
                    <Tooltip title="编辑模板">
                      <Button
                        type="text"
                        icon={<EditOutlined />}
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditTemplate(item);
                        }}
                      />
                    </Tooltip>
                    <Tooltip title="删除模板">
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        size="small"
                        onClick={(e) => handleDelete(item, e)}
                      />
                    </Tooltip>
                  </Space>
                </div>
              </Card>
            </List.Item>
          )}

        />
      </Card>

      <Modal
        title="模板详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={700}
      >
        {renderTemplateDetail()}
      </Modal>

      <Modal
        title="创建流程模板"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateTemplate}
        >
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本信息" key="basic">
              <Form.Item
                name="title"
                label="模板名称"
                rules={[{ required: true, message: '请输入模板名称' }]}
              >
                <Input placeholder="请输入模板名称" />
              </Form.Item>

              <Form.Item
                name="type"
                label="流程类型"
                rules={[{ required: true, message: '请选择流程类型' }]}
              >
                <Select placeholder="请选择流程类型">
                  <Option value="报销申请">报销申请</Option>
                  <Option value="休假申请">休假申请</Option>
                  <Option value="采购申请">采购申请</Option>
                  <Option value="项目申请">项目申请</Option>
                  <Option value="工时申请">工时申请</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="department"
                label="所属部门"
                rules={[{ required: true, message: '请选择所属部门' }]}
              >
                <Select placeholder="请选择所属部门">
                  <Option value="全公司">全公司</Option>
                  <Option value="技术部">技术部</Option>
                  <Option value="行政部">行政部</Option>
                  <Option value="财务部">财务部</Option>
                  <Option value="人事部">人事部</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="description"
                label="模板描述"
                rules={[{ required: true, message: '请输入模板描述' }]}
              >
                <Input.TextArea rows={4} placeholder="请输入模板描述" />
              </Form.Item>

              <Form.Item
                name="status"
                label="模板状态"
                initialValue="启用"
              >
                <Radio.Group>
                  <Radio value="启用">启用</Radio>
                  <Radio value="禁用">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </TabPane>

            <TabPane tab="审批流程" key="approval">
              <Form.List name="approvalFlow">
                {(fields, { add, remove }) => (
                  <div className="approval-flow-config">
                    {fields.map((field, index) => (
                      <div key={field.key} className="approval-node-config">
                        <Card
                          title={`审批节点 ${index + 1}`}
                          extra={
                            <Button
                              type="text"
                              danger
                              icon={<MinusCircleOutlined />}
                              onClick={() => remove(field.name)}
                            />
                          }
                        >
                          <Form.Item
                            {...field}
                            label="审批人类型"
                            name={[field.name, 'type']}
                            rules={[{ required: true, message: '请选择审批人类型' }]}
                          >
                            <Select>
                              <Option value="role">角色</Option>
                              <Option value="user">用户</Option>
                            </Select>
                          </Form.Item>

                          <Form.Item
                            {...field}
                            label="审批人"
                            name={[field.name, 'approver']}
                            rules={[{ required: true, message: '请输入审批人' }]}
                          >
                            <Input />
                          </Form.Item>

                          <Form.Item
                            {...field}
                            label="说明"
                            name={[field.name, 'description']}
                          >
                            <Input />
                          </Form.Item>
                        </Card>
                      </div>
                    ))}

                    <Button
                      type="dashed"
                      onClick={() => add()}
                      block
                      icon={<PlusCircleOutlined />}
                    >
                      添加审批节点
                    </Button>
                  </div>
                )}
              </Form.List>
            </TabPane>

            <TabPane tab="表单字段" key="form">
              <Form.List name="formFields">
                {(fields, { add, remove }) => (
                  <div className="form-fields-config">
                    {fields.map((field, index) => (
                      <div key={field.key} className="field-config">
                        <Card
                          title={`字段 ${index + 1}`}
                          extra={
                            <Button
                              type="text"
                              danger
                              icon={<MinusCircleOutlined />}
                              onClick={() => remove(field.name)}
                            />
                          }
                        >
                          <Form.Item
                            {...field}
                            label="字段名称"
                            name={[field.name, 'label']}
                            rules={[{ required: true, message: '请输入字段名称' }]}
                          >
                            <Input />
                          </Form.Item>

                          <Form.Item
                            {...field}
                            label="字段类型"
                            name={[field.name, 'type']}
                            rules={[{ required: true, message: '请选择字段类型' }]}
                          >
                            <Select>
                              <Option value="text">文本</Option>
                              <Option value="textarea">多行文本</Option>
                              <Option value="number">数字</Option>
                              <Option value="date">日期</Option>
                              <Option value="dateRange">日期区间</Option>
                              <Option value="select">下拉选择</Option>
                              <Option value="radio">单选</Option>
                              <Option value="checkbox">多选</Option>
                              <Option value="upload">文件上传</Option>
                              <Option value="table">表格</Option>
                            </Select>
                          </Form.Item>

                          <Form.Item
                            {...field}
                            label="是否必填"
                            name={[field.name, 'required']}
                            valuePropName="checked"
                          >
                            <Switch />
                          </Form.Item>
                        </Card>
                      </div>
                    ))}

                    <Button
                      type="dashed"
                      onClick={() => add()}
                      block
                      icon={<PlusCircleOutlined />}
                    >
                      添加表单字段
                    </Button>
                  </div>
                )}
              </Form.List>
            </TabPane>
          </Tabs>

          <div className="form-actions">
            <Space>
              <Button onClick={() => setCreateModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">创建</Button>
            </Space>
          </div>
        </Form>
      </Modal>

      <Modal
        title="编辑流程模板"
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveTemplate}
        >
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本信息" key="basic">
              <Form.Item
                name="title"
                label="模板名称"
                rules={[{ required: true, message: '请输入模板名称' }]}
              >
                <Input placeholder="请输入模板名称" />
              </Form.Item>

              <Form.Item
                name="type"
                label="流程类型"
                rules={[{ required: true, message: '请选择流程类型' }]}
              >
                <Select placeholder="请选择流程类型">
                  <Option value="报销申请">报销申请</Option>
                  <Option value="休假申请">休假申请</Option>
                  <Option value="采购申请">采购申请</Option>
                  <Option value="项目申请">项目申请</Option>
                  <Option value="工时申请">工时申请</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="department"
                label="所属部门"
                rules={[{ required: true, message: '请选择所属部门' }]}
              >
                <Select placeholder="请选择所属部门">
                  <Option value="全公司">全公司</Option>
                  <Option value="技术部">技术部</Option>
                  <Option value="行政部">行政部</Option>
                  <Option value="财务部">财务部</Option>
                  <Option value="人事部">人事部</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="description"
                label="模板描述"
                rules={[{ required: true, message: '请输入模板描述' }]}
              >
                <Input.TextArea rows={4} placeholder="请输入模板描述" />
              </Form.Item>

              <Form.Item
                name="status"
                label="模板状态"
                initialValue="启用"
              >
                <Radio.Group>
                  <Radio value="启用">启用</Radio>
                  <Radio value="禁用">禁用</Radio>
                </Radio.Group>
              </Form.Item>
            </TabPane>

            <TabPane tab="审批流程" key="approval">
              <Form.List name="approvalFlow">
                {(fields, { add, remove }) => (
                  <div className="approval-flow-config">
                    {fields.map((field, index) => (
                      <div key={field.key} className="approval-node-config">
                        <Card
                          title={`审批节点 ${index + 1}`}
                          extra={
                            <Button
                              type="text"
                              danger
                              icon={<MinusCircleOutlined />}
                              onClick={() => remove(field.name)}
                            />
                          }
                        >
                          <Form.Item
                            {...field}
                            label="审批人类型"
                            name={[field.name, 'type']}
                            rules={[{ required: true, message: '请选择审批人类型' }]}
                          >
                            <Select>
                              <Option value="role">角色</Option>
                              <Option value="user">用户</Option>
                            </Select>
                          </Form.Item>

                          <Form.Item
                            {...field}
                            label="审批人"
                            name={[field.name, 'approver']}
                            rules={[{ required: true, message: '请输入审批人' }]}
                          >
                            <Input />
                          </Form.Item>

                          <Form.Item
                            {...field}
                            label="说明"
                            name={[field.name, 'description']}
                          >
                            <Input />
                          </Form.Item>
                        </Card>
                      </div>
                    ))}

                    <Button
                      type="dashed"
                      onClick={() => add()}
                      block
                      icon={<PlusCircleOutlined />}
                    >
                      添加审批节点
                    </Button>
                  </div>
                )}
              </Form.List>
            </TabPane>

            <TabPane tab="表单字段" key="form">
              <Form.List name="formFields">
                {(fields, { add, remove }) => (
                  <div className="form-fields-config">
                    {fields.map((field, index) => (
                      <div key={field.key} className="field-config">
                        <Card
                          title={`字段 ${index + 1}`}
                          extra={
                            <Button
                              type="text"
                              danger
                              icon={<MinusCircleOutlined />}
                              onClick={() => remove(field.name)}
                            />
                          }
                        >
                          <Form.Item
                            {...field}
                            label="字段名称"
                            name={[field.name, 'label']}
                            rules={[{ required: true, message: '请输入字段名称' }]}
                          >
                            <Input />
                          </Form.Item>

                          <Form.Item
                            {...field}
                            label="字段类型"
                            name={[field.name, 'type']}
                            rules={[{ required: true, message: '请选择字段类型' }]}
                          >
                            <Select>
                              <Option value="text">文本</Option>
                              <Option value="textarea">多行文本</Option>
                              <Option value="number">数字</Option>
                              <Option value="date">日期</Option>
                              <Option value="dateRange">日期区间</Option>
                              <Option value="select">下拉选择</Option>
                              <Option value="radio">单选</Option>
                              <Option value="checkbox">多选</Option>
                              <Option value="upload">文件上传</Option>
                              <Option value="table">表格</Option>
                            </Select>
                          </Form.Item>

                          <Form.Item
                            {...field}
                            label="是否必填"
                            name={[field.name, 'required']}
                            valuePropName="checked"
                          >
                            <Switch />
                          </Form.Item>
                        </Card>
                      </div>
                    ))}

                    <Button
                      type="dashed"
                      onClick={() => add()}
                      block
                      icon={<PlusCircleOutlined />}
                    >
                      添加表单字段
                    </Button>
                  </div>
                )}
              </Form.List>
            </TabPane>
          </Tabs>

          <div className="form-actions">
            <Space>
              <Button onClick={() => setEditModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">保存</Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default ProcessTemplates;
