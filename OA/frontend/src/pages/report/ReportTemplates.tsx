import React, { useState, useEffect } from 'react';
import {
  Card,
  Input,
  Button,
  Tag,
  Drawer,
  Space,
  Divider,
  Empty,
  Spin,
  Pagination,
  Select,
  message,
  Modal,
  Typography,
  Avatar,
  Badge
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  DownloadOutlined,
  UserOutlined,
  ClockCircleOutlined,
  TagsOutlined,
  FileTextOutlined,
  StarOutlined,
  StarFilled,
  SettingOutlined,
  BarChartOutlined,
  LineChartOutlined,
  PieChartOutlined,
  DotChartOutlined,
  RadarChartOutlined,
  FundOutlined
} from '@ant-design/icons';
import '../../styles/pages/report/ReportTemplates.less';

const { Title, Paragraph } = Typography;
const { Option } = Select;

// 模拟数据
const mockCategories = [
  { id: '1', name: '销售报告', count: 8 },
  { id: '2', name: '财务报告', count: 12 },
  { id: '3', name: '项目报告', count: 15 },
  { id: '4', name: '人力资源', count: 6 },
  { id: '5', name: '市场分析', count: 9 },
  { id: '6', name: '客户调研', count: 4 },
  { id: '7', name: '产品分析', count: 7 },
];

// 为不同类型的报告模板创建预览图片URL
// 通常这些URL会指向实际存储的图片，这里使用Base64编码的SVG图片作为示例
const generateChartSvg = (type: string): string => {
  // 创建SVG图表的颜色方案
  const colorPrimary = '#1890ff';
  const colorSecondary = '#52c41a';
  const colorAccent = '#f5222d';
  const colorNeutral = '#faad14';
  const bgColor = '#f5f5f5';

  // 不同类型的模板使用不同样式的图表SVG
  switch (type) {
    case '销售报告':
      // 柱状图 + 折线图
      return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 160" width="240" height="160">
        <rect width="240" height="160" fill="${bgColor}"/>
        <g transform="translate(20,20)">
          <line x1="0" y1="100" x2="200" y2="100" stroke="#ccc" stroke-width="1"/>
          <line x1="0" y1="0" x2="0" y2="100" stroke="#ccc" stroke-width="1"/>
          <rect x="15" y="30" width="20" height="70" fill="${colorPrimary}" opacity="0.8"/>
          <rect x="55" y="20" width="20" height="80" fill="${colorPrimary}" opacity="0.8"/>
          <rect x="95" y="40" width="20" height="60" fill="${colorPrimary}" opacity="0.8"/>
          <rect x="135" y="10" width="20" height="90" fill="${colorPrimary}" opacity="0.8"/>
          <rect x="175" y="25" width="20" height="75" fill="${colorPrimary}" opacity="0.8"/>
          <polyline points="25,20 65,10 105,30 145,5 185,15" fill="none" stroke="${colorAccent}" stroke-width="2"/>
          <circle cx="25" cy="20" r="3" fill="${colorAccent}"/>
          <circle cx="65" cy="10" r="3" fill="${colorAccent}"/>
          <circle cx="105" cy="30" r="3" fill="${colorAccent}"/>
          <circle cx="145" cy="5" r="3" fill="${colorAccent}"/>
          <circle cx="185" cy="15" r="3" fill="${colorAccent}"/>
        </g>
      </svg>`;
    case '财务报告':
      // 饼图 + 折线图
      return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 160" width="240" height="160">
        <rect width="240" height="160" fill="${bgColor}"/>
        <g transform="translate(60, 80)">
          <path d="M0,0 L50,0 A50,50 0 0,1 25,43.3 z" fill="${colorPrimary}" opacity="0.8"/>
          <path d="M0,0 L25,43.3 A50,50 0 0,1 -32.7,37.6 z" fill="${colorSecondary}" opacity="0.8"/>
          <path d="M0,0 L-32.7,37.6 A50,50 0 0,1 -40.5,-30 z" fill="${colorNeutral}" opacity="0.8"/>
          <path d="M0,0 L-40.5,-30 A50,50 0 0,1 0,-50 z" fill="${colorAccent}" opacity="0.8"/>
          <path d="M0,0 L0,-50 A50,50 0 0,1 50,0 z" fill="#8c8c8c" opacity="0.8"/>
        </g>
        <g transform="translate(160, 60)">
          <line x1="0" y1="60" x2="60" y2="60" stroke="#ccc" stroke-width="1"/>
          <polyline points="5,40 20,20 35,30 50,10" fill="none" stroke="${colorPrimary}" stroke-width="2"/>
          <circle cx="5" cy="40" r="2" fill="${colorPrimary}"/>
          <circle cx="20" cy="20" r="2" fill="${colorPrimary}"/>
          <circle cx="35" cy="30" r="2" fill="${colorPrimary}"/>
          <circle cx="50" cy="10" r="2" fill="${colorPrimary}"/>
        </g>
      </svg>`;
    case '项目报告':
      // 甘特图样式
      return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 160" width="240" height="160">
        <rect width="240" height="160" fill="${bgColor}"/>
        <g transform="translate(20,20)">
          <line x1="0" y1="0" x2="0" y2="120" stroke="#ccc" stroke-width="1"/>
          <line x1="0" y1="20" x2="200" y2="20" stroke="#eee" stroke-width="1"/>
          <line x1="0" y1="40" x2="200" y2="40" stroke="#eee" stroke-width="1"/>
          <line x1="0" y1="60" x2="200" y2="60" stroke="#eee" stroke-width="1"/>
          <line x1="0" y1="80" x2="200" y2="80" stroke="#eee" stroke-width="1"/>
          <line x1="0" y1="100" x2="200" y2="100" stroke="#eee" stroke-width="1"/>
          <rect x="10" y="15" width="80" height="10" rx="2" fill="${colorPrimary}"/>
          <rect x="30" y="35" width="120" height="10" rx="2" fill="${colorSecondary}"/>
          <rect x="20" y="55" width="40" height="10" rx="2" fill="${colorNeutral}"/>
          <rect x="70" y="55" width="70" height="10" rx="2" fill="${colorPrimary}"/>
          <rect x="50" y="75" width="100" height="10" rx="2" fill="${colorAccent}"/>
          <rect x="10" y="95" width="180" height="10" rx="2" fill="${colorSecondary}"/>
        </g>
      </svg>`;
    case '人力资源':
      // 雷达图
      return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 160" width="240" height="160">
        <rect width="240" height="160" fill="${bgColor}"/>
        <g transform="translate(120, 80)">
          <polygon points="0,-60 57,19 35,48 -35,48 -57,19" fill="${colorPrimary}" opacity="0.3" stroke="${colorPrimary}" stroke-width="2"/>
          <line x1="0" y1="0" x2="0" y2="-60" stroke="#ccc" stroke-width="1"/>
          <line x1="0" y1="0" x2="57" y2="19" stroke="#ccc" stroke-width="1"/>
          <line x1="0" y1="0" x2="35" y2="48" stroke="#ccc" stroke-width="1"/>
          <line x1="0" y1="0" x2="-35" y2="48" stroke="#ccc" stroke-width="1"/>
          <line x1="0" y1="0" x2="-57" y2="19" stroke="#ccc" stroke-width="1"/>
          <circle cx="0" cy="-60" r="3" fill="${colorPrimary}"/>
          <circle cx="57" cy="19" r="3" fill="${colorPrimary}"/>
          <circle cx="35" cy="48" r="3" fill="${colorPrimary}"/>
          <circle cx="-35" cy="48" r="3" fill="${colorPrimary}"/>
          <circle cx="-57" cy="19" r="3" fill="${colorPrimary}"/>
        </g>
      </svg>`;
    case '市场分析':
      // 散点图 + 趋势线
      return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 160" width="240" height="160">
        <rect width="240" height="160" fill="${bgColor}"/>
        <g transform="translate(20,20)">
          <line x1="0" y1="120" x2="200" y2="120" stroke="#ccc" stroke-width="1"/>
          <line x1="0" y1="0" x2="0" y2="120" stroke="#ccc" stroke-width="1"/>
          <line x1="0" y1="40" x2="200" y2="0" stroke="${colorPrimary}" stroke-width="2" stroke-dasharray="4 2"/>
          <circle cx="30" cy="70" r="5" fill="${colorPrimary}" opacity="0.7"/>
          <circle cx="50" cy="90" r="8" fill="${colorSecondary}" opacity="0.7"/>
          <circle cx="80" cy="50" r="6" fill="${colorAccent}" opacity="0.7"/>
          <circle cx="110" cy="30" r="9" fill="${colorNeutral}" opacity="0.7"/>
          <circle cx="140" cy="60" r="4" fill="${colorPrimary}" opacity="0.7"/>
          <circle cx="170" cy="20" r="7" fill="${colorSecondary}" opacity="0.7"/>
        </g>
      </svg>`;
    case '客户调研':
      // 仪表盘 + 条形图
      return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 160" width="240" height="160">
        <rect width="240" height="160" fill="${bgColor}"/>
        <g transform="translate(80, 70)">
          <path d="M0,0 A40,40 0 1,1 0,-40 L 0,0 Z" fill="${colorAccent}" opacity="0.8"/>
          <path d="M0,0 A40,40 0 0,1 -40,0 L 0,0 Z" fill="${colorPrimary}" opacity="0.8"/>
          <path d="M0,0 A40,40 0 0,1 40,0 L 0,0 Z" fill="${colorNeutral}" opacity="0.8"/>
          <circle cx="0" cy="0" r="5" fill="#fff" stroke="#666" stroke-width="1"/>
          <line x1="0" y1="0" x2="22" y2="-22" stroke="#333" stroke-width="2"/>
        </g>
        <g transform="translate(150, 30)">
          <rect x="0" y="5" width="70" height="10" fill="${colorPrimary}"/>
          <rect x="0" y="25" width="50" height="10" fill="${colorSecondary}"/>
          <rect x="0" y="45" width="40" height="10" fill="${colorNeutral}"/>
          <rect x="0" y="65" width="60" height="10" fill="${colorAccent}"/>
        </g>
      </svg>`;
    default:
      // 默认图表组合
      return `data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 160" width="240" height="160">
        <rect width="240" height="160" fill="${bgColor}"/>
        <g transform="translate(20,20)">
          <line x1="0" y1="120" x2="200" y2="120" stroke="#ccc" stroke-width="1"/>
          <line x1="0" y1="0" x2="0" y2="120" stroke="#ccc" stroke-width="1"/>
          <rect x="20" y="40" width="30" height="80" fill="${colorPrimary}" opacity="0.8"/>
          <rect x="60" y="60" width="30" height="60" fill="${colorSecondary}" opacity="0.8"/>
          <rect x="100" y="20" width="30" height="100" fill="${colorNeutral}" opacity="0.8"/>
          <rect x="140" y="50" width="30" height="70" fill="${colorAccent}" opacity="0.8"/>
        </g>
      </svg>`;
  }
};

// 获取与报告类型匹配的图标
const getCategoryIcon = (category: string) => {
  switch (category) {
    case '销售报告':
      return <BarChartOutlined style={{ fontSize: 16 }} />;
    case '财务报告':
      return <PieChartOutlined style={{ fontSize: 16 }} />;
    case '项目报告':
      return <FundOutlined style={{ fontSize: 16 }} />;
    case '人力资源':
      return <RadarChartOutlined style={{ fontSize: 16 }} />;
    case '市场分析':
      return <DotChartOutlined style={{ fontSize: 16 }} />;
    case '客户调研':
      return <LineChartOutlined style={{ fontSize: 16 }} />;
    default:
      return <BarChartOutlined style={{ fontSize: 16 }} />;
  }
};

const mockTemplates = [
  {
    id: '1',
    title: '月度销售报告',
    category: '销售报告',
    description: '全面分析月度销售数据，包括销售额、订单量、客户数等关键指标，支持多维度数据对比。',
    author: '张三',
    department: '销售部',
    createTime: '2025-01-15',
    updateTime: '2025-02-20',
    usageCount: 128,
    previewImage: "http://*************:31114/tmp/20250330-234306.jpeg", //generateChartSvg('销售报告'),
    tags: ['销售', '月报', '数据分析'],
    isFavorite: true,
  },
  {
    id: '2',
    title: '季度财务分析报告',
    category: '财务报告',
    description: '详细分析季度财务状况，包括收入、支出、利润等财务指标，提供同比环比分析。',
    author: '李四',
    department: '财务部',
    createTime: '2024-12-10',
    updateTime: '2025-03-05',
    usageCount: 86,
    previewImage: "http://*************:31114/tmp/20250330-234337.jpeg", //generateChartSvg('财务报告'),
    tags: ['财务', '季报', '财务分析'],
    isFavorite: false,
  },
  {
    id: '3',
    title: '项目进度报告',
    category: '项目报告',
    description: '跟踪项目进展情况，包括任务完成度、里程碑达成情况、资源使用情况等。',
    author: '王五',
    department: '项目管理部',
    createTime: '2025-02-05',
    updateTime: '2025-03-01',
    usageCount: 156,
    previewImage: "http://*************:31114/tmp/20250330-234326.jpeg", //generateChartSvg('项目报告'),
    tags: ['项目管理', '进度跟踪', '里程碑'],
    isFavorite: true,
  },
  {
    id: '4',
    title: '员工绩效评估报告',
    category: '人力资源',
    description: '全面评估员工工作表现，包括KPI达成情况、能力评估、发展建议等。',
    author: '赵六',
    department: '人力资源部',
    createTime: '2025-01-20',
    updateTime: '2025-02-15',
    usageCount: 92,
    previewImage: "http://*************:31114/tmp/20250330-234330.jpeg", //generateChartSvg('人力资源'),
    tags: ['人力资源', '绩效', '评估'],
    isFavorite: false,
  },
  {
    id: '5',
    title: '市场竞争分析报告',
    category: '市场分析',
    description: '分析市场竞争格局，包括竞争对手分析、市场份额、SWOT分析等。',
    author: '孙七',
    department: '市场部',
    createTime: '2024-11-30',
    updateTime: '2025-01-25',
    usageCount: 113,
    previewImage: "http://*************:31114/tmp/20250330-234334.jpeg", //generateChartSvg('市场分析'),
    tags: ['市场', '竞争分析', 'SWOT'],
    isFavorite: false,
  },
  {
    id: '6',
    title: '客户满意度调研报告',
    category: '客户调研',
    description: '分析客户满意度调研结果，包括NPS评分、满意度指标、客户反馈等。',
    author: '周八',
    department: '客户服务部',
    createTime: '2025-02-10',
    updateTime: '2025-03-10',
    usageCount: 78,
    previewImage: "http://*************:31114/tmp/20250330-234320.jpeg", //generateChartSvg('客户调研'),
    tags: ['客户', '满意度', 'NPS'],
    isFavorite: true,
  },
];

const ReportTemplates: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState<any[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<any[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [totalTemplates, setTotalTemplates] = useState(0);

  // 模拟加载数据
  useEffect(() => {
    setLoading(true);
    // 模拟API请求延迟
    setTimeout(() => {
      setTemplates(mockTemplates);
      setFilteredTemplates(mockTemplates);
      setTotalTemplates(mockTemplates.length);
      setLoading(false);
    }, 1000);
  }, []);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    filterTemplates(value, selectedCategory);
  };

  // 处理分类筛选
  const handleCategoryFilter = (categoryId: string | null) => {
    setSelectedCategory(categoryId);
    filterTemplates(searchValue, categoryId);
  };

  // 筛选模板
  const filterTemplates = (search: string, categoryId: string | null) => {
    let filtered = [...templates];

    // 搜索筛选
    if (search) {
      filtered = filtered.filter(template =>
        template.title.toLowerCase().includes(search.toLowerCase()) ||
        template.description.toLowerCase().includes(search.toLowerCase()) ||
        template.tags.some((tag: string) => tag.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // 分类筛选
    if (categoryId) {
      const category = mockCategories.find(c => c.id === categoryId);
      if (category) {
        filtered = filtered.filter(template => template.category === category.name);
      }
    }

    setFilteredTemplates(filtered);
    setTotalTemplates(filtered.length);
    setCurrentPage(1);
  };

  // 处理模板查看
  const handleViewTemplate = (template: any) => {
    setCurrentTemplate(template);
    setDrawerVisible(true);
  };

  // 处理模板编辑
  const handleEditTemplate = (template: any) => {
    message.info(`编辑模板: ${template.title}`);
    // 跳转到模板编辑页面或打开编辑对话框
  };

  // 处理模板复制
  const handleCopyTemplate = (template: any) => {
    message.success(`已创建模板副本: ${template.title} - 副本`);
    // 创建模板副本的逻辑
  };

  // 处理模板删除
  const handleDeleteTemplate = (template: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除模板"${template.title}"吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        message.success(`已删除模板: ${template.title}`);
        // 删除模板的逻辑
        const updatedTemplates = templates.filter(t => t.id !== template.id);
        setTemplates(updatedTemplates);
        filterTemplates(searchValue, selectedCategory);
      },
    });
  };

  // 处理收藏/取消收藏
  const handleToggleFavorite = (template: any) => {
    const updatedTemplates = templates.map(t => {
      if (t.id === template.id) {
        return { ...t, isFavorite: !t.isFavorite };
      }
      return t;
    });
    setTemplates(updatedTemplates);
    setFilteredTemplates(
      filteredTemplates.map(t => {
        if (t.id === template.id) {
          return { ...t, isFavorite: !t.isFavorite };
        }
        return t;
      })
    );

    message.success(template.isFavorite ?
      `已取消收藏: ${template.title}` :
      `已收藏: ${template.title}`
    );
  };

  // 处理创建新模板
  const handleCreateTemplate = () => {
    message.info('创建新模板功能即将上线');
    // 跳转到模板创建页面
  };

  // 处理分页变化
  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    if (pageSize) setPageSize(pageSize);
  };

  // 获取当前页的模板
  const getCurrentPageTemplates = () => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredTemplates.slice(startIndex, endIndex);
  };

  return (
    <div className="report-templates-container">
      <div className="page-header">
        <Title level={2}>报告模板</Title>
        <Paragraph>浏览和管理报告模板，快速创建标准化报告</Paragraph>
      </div>

      {/* 分类区域 */}
      <div className="categories-section">
        <div className="categories-title">
          <Title level={4}>模板分类</Title>
          <Button type="link" onClick={() => handleCategoryFilter(null)}>
            {selectedCategory ? '清除筛选' : '查看全部'}
          </Button>
        </div>
        <div className="categories-list">
          {mockCategories.map(category => (
            <Button
              key={category.id}
              type={selectedCategory === category.id ? 'primary' : 'default'}
              onClick={() => handleCategoryFilter(category.id)}
            >
              {category.name} ({category.count})
            </Button>
          ))}
        </div>
      </div>

      {/* 搜索和操作区域 */}
      <div className="templates-actions">
        <div className="search-filter">
          <Input
            placeholder="搜索模板"
            prefix={<SearchOutlined />}
            value={searchValue}
            onChange={e => handleSearch(e.target.value)}
            allowClear
          />
          <Select
            placeholder="排序方式"
            style={{ width: 150 }}
            defaultValue="updateTime"
          >
            <Option value="updateTime">最近更新</Option>
            <Option value="createTime">创建时间</Option>
            <Option value="usageCount">使用次数</Option>
            <Option value="title">名称</Option>
          </Select>
        </div>
        <div className="action-buttons">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateTemplate}
          >
            创建模板
          </Button>
        </div>
      </div>

      {/* 模板网格 */}
      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>加载模板中...</div>
        </div>
      ) : filteredTemplates.length === 0 ? (
        <Empty
          description="未找到匹配的模板"
          style={{ margin: '40px 0' }}
        />
      ) : (
        <div className="templates-grid">
          {getCurrentPageTemplates().map(template => (
            <Card
              key={template.id}
              className="template-card"
              hoverable
            >
              <div className="template-header">
                <div className="template-title">{template.title}</div>
                <div className="template-category">
                  {getCategoryIcon(template.category)} {template.category}
                </div>
              </div>

              <div className="template-preview" onClick={() => handleViewTemplate(template)}>
                {template.previewImage ? (
                  <img src={template.previewImage} alt={template.title} />
                ) : (
                  <div className="preview-placeholder">
                    <FileTextOutlined style={{ fontSize: 48, opacity: 0.5 }} />
                    <div>点击预览模板</div>
                  </div>
                )}
              </div>

              <div className="template-info">
                <div className="template-description">{template.description}</div>
                <div className="template-meta">
                  <div className="template-author">
                    <Avatar size="small" icon={<UserOutlined />} />
                    {template.author}
                  </div>
                  <div className="template-date">
                    <ClockCircleOutlined /> {template.updateTime}
                  </div>
                </div>
              </div>

              <div className="template-tags">
                {template.tags.map((tag: string, index: number) => (
                  <Tag key={index}>{tag}</Tag>
                ))}
              </div>

              <div className="template-actions">
                <Button
                  type="primary"
                  icon={<EyeOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleViewTemplate(template);
                  }}
                >
                  查看模板
                </Button>
                <Space>
                  <Button
                    icon={<EditOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditTemplate(template);
                    }}
                  >
                    编辑
                  </Button>
                  <Button
                    icon={<CopyOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCopyTemplate(template);
                    }}
                  >
                    复制
                  </Button>
                </Space>
                <Space>
                  <Button
                    icon={template.isFavorite ? <StarFilled style={{ color: '#FFB400' }} /> : <StarOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleToggleFavorite(template);
                    }}
                  >
                    {template.isFavorite ? '取消收藏' : '收藏'}
                  </Button>
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteTemplate(template);
                    }}
                  >
                    删除
                  </Button>
                </Space>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* 分页 */}
      {filteredTemplates.length > 0 && (
        <div className="pagination-container">
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={totalTemplates}
            onChange={handlePageChange}
            showSizeChanger
            showQuickJumper
            showTotal={total => `共 ${total} 个模板`}
          />
        </div>
      )}

      {/* 模板详情抽屉 */}
      <Drawer
        title="模板详情"
        placement="right"
        width={720}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        extra={
          <Space>
            <Button
              icon={currentTemplate?.isFavorite ? <StarFilled style={{ color: '#FFB400' }} /> : <StarOutlined />}
              onClick={() => currentTemplate && handleToggleFavorite(currentTemplate)}
            >
              {currentTemplate?.isFavorite ? '取消收藏' : '收藏'}
            </Button>
            <Button icon={<SettingOutlined />}>设置</Button>
          </Space>
        }
      >
        {currentTemplate && (
          <div className="drawer-content">
            <div className="template-detail-header">
              <div className="template-detail-title">
                <Title level={3}>{currentTemplate.title}</Title>
                <Badge count={currentTemplate.usageCount} overflowCount={999} title={`已使用 ${currentTemplate.usageCount} 次`}>
                  <Button icon={<DownloadOutlined />}>使用模板</Button>
                </Badge>
              </div>

              <div className="template-detail-meta">
                <Space>
                  <span>
                    <UserOutlined /> 创建人: {currentTemplate.author} ({currentTemplate.department})
                  </span>
                  <span>
                    <ClockCircleOutlined /> 更新时间: {currentTemplate.updateTime}
                  </span>
                </Space>
                <span>分类: {currentTemplate.category}</span>
              </div>

              <div className="template-detail-tags">
                <TagsOutlined /> 标签:
                {currentTemplate.tags.map((tag: string, index: number) => (
                  <Tag key={index} style={{ marginLeft: 8 }}>{tag}</Tag>
                ))}
              </div>

              <div className="template-detail-description">
                <Paragraph>{currentTemplate.description}</Paragraph>
              </div>

              <Divider />
            </div>

            <div className="template-detail-preview">
              {currentTemplate.previewImage ? (
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                  <img
                    src={currentTemplate.previewImage}
                    alt={currentTemplate.title}
                    style={{ maxWidth: '100%', height: 'auto', border: '1px solid #eee', borderRadius: '4px' }}
                  />
                  <div style={{ marginTop: 16, fontWeight: 'bold' }}>模板预览</div>
                  <div style={{ marginTop: 8, color: '#888' }}>
                    包含报告结构、章节布局、图表位置等
                  </div>
                </div>
              ) : (
                <div style={{ textAlign: 'center', padding: '40px 0' }}>
                  <FileTextOutlined style={{ fontSize: 64, opacity: 0.5 }} />
                  <div style={{ marginTop: 16 }}>模板预览内容将在此显示</div>
                  <div style={{ marginTop: 8, color: '#888' }}>
                    包含报告结构、章节布局、图表位置等
                  </div>
                </div>
              )}
            </div>

            <div className="template-detail-actions">
              <Space>
                <Button icon={<EditOutlined />} onClick={() => handleEditTemplate(currentTemplate)}>
                  编辑模板
                </Button>
                <Button icon={<CopyOutlined />} onClick={() => handleCopyTemplate(currentTemplate)}>
                  创建副本
                </Button>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={() => {
                    message.success(`开始使用模板: ${currentTemplate.title}`);
                    // 跳转到报告生成页面并选择此模板
                  }}
                >
                  使用此模板
                </Button>
              </Space>
            </div>
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default ReportTemplates;
