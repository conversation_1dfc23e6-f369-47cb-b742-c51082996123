import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Select,
  Button,
  Input,
  DatePicker,
  Switch,
  Spin,
  Empty,
  message,
  Tooltip,
  List,
  Space,
  Typography
} from 'antd';
import {
  DownloadOutlined,
  PrinterOutlined,
  ShareAltOutlined,
  SaveOutlined,
  EyeOutlined,
  SettingOutlined,
  RobotOutlined,
  LineChartOutlined,
  FileTextOutlined,
  HistoryOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import '../../styles/pages/report/ReportGeneration.less';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

// 模拟数据
const mockTemplates = [
  { id: '1', name: '月度销售报告', description: '用于生成销售部门的月度业绩报告' },
  { id: '2', name: '项目进度报告', description: '用于跟踪项目的完成情况和里程碑' },
  { id: '3', name: '财务分析报告', description: '用于财务部门的季度财务分析' },
  { id: '4', name: '人力资源报告', description: '用于人力资源部门的员工情况统计' },
];

const mockDataSources = [
  { id: '1', name: 'CRM系统', type: 'database' },
  { id: '2', name: '销售数据表', type: 'excel' },
  { id: '3', name: '客户反馈', type: 'api' },
  { id: '4', name: '员工考勤系统', type: 'database' },
];

const mockHistoryReports = [
  {
    id: '1',
    title: '2025年2月销售报告',
    template: '月度销售报告',
    creator: '张三',
    createTime: '2025-03-01 14:30:00',
    status: 'completed'
  },
  {
    id: '2',
    title: '项目A第一季度进度报告',
    template: '项目进度报告',
    creator: '李四',
    createTime: '2025-02-15 09:45:00',
    status: 'completed'
  },
  {
    id: '3',
    title: '2025年Q1财务分析',
    template: '财务分析报告',
    creator: '王五',
    createTime: '2025-01-20 16:15:00',
    status: 'completed'
  },
];

const ReportGeneration: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [previewContent, setPreviewContent] = useState<any>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  // 模拟加载数据
  useEffect(() => {
    setLoading(true);
    // 模拟API请求延迟
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  // 处理模板选择
  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplate(templateId);
    // 根据模板ID加载模板默认参数
    const template = mockTemplates.find(t => t.id === templateId);
    if (template) {
      form.setFieldsValue({
        reportTitle: `${template.name} - ${new Date().toLocaleDateString('zh-CN')}`,
        // 其他默认值...
      });
    }
  };

  // 生成报告预览
  const handleGeneratePreview = () => {
    form.validateFields().then(values => {
      setGenerating(true);
      // 模拟API请求生成报告
      setTimeout(() => {
        setGenerating(false);
        setPreviewContent({
          title: values.reportTitle,
          date: new Date().toLocaleDateString('zh-CN'),
          sections: [
            {
              title: '摘要',
              content: '以下是2025年3月的月度销售报告摘要。本月销售额达到120万元，同比增长15%，订单量为1,500单，同比增长10%。客户满意度保持在90%以上，较上月有所提升。',
            },
            {
              title: '数据分析',
              content: '基于所选数据源的详细月度销售数据分析结果，显示销售额在3月呈现稳步增长趋势。主要增长来源于新客户的增加和现有客户的复购率提升。订单量的增加主要得益于促销活动的成功实施。客户满意度的提升主要归功于售后服务的优化。',
              chart: true,
              image: "http://*************:31114/tmp/zzz.png", // 添加图片
            },
            {
              title: '结论',
              content: '根据月度数据分析结果，我们可以得出以下结论：1）销售额和订单量均实现两位数增长，表明市场策略有效；2）客户满意度提升，说明客户体验得到改善；3）未来应继续优化售后服务，同时探索新的市场机会以保持增长态势。',
            },
          ]
        });
        message.success('报告预览生成成功');
      }, 2000);
    }).catch(err => {
      console.error('表单验证失败:', err);
    });
  };

  // 处理报告下载
  const handleDownload = () => {
    message.success('报告已开始下载');
  };

  // 处理报告打印
  const handlePrint = () => {
    message.info('正在准备打印...');
  };

  // 处理报告分享
  const handleShare = () => {
    message.info('分享功能即将上线');
  };

  // 处理报告保存
  const handleSave = () => {
    message.success('报告已保存');
  };

  // 加载历史报告
  const handleLoadHistory = (reportId: string) => {
    const report = mockHistoryReports.find(r => r.id === reportId);
    if (report) {
      setLoading(true);
      setTimeout(() => {
        setLoading(false);
        setPreviewContent({
          title: report.title,
          date: report.createTime.split(' ')[0],
          sections: [
            {
              title: '摘要',
              content: '这是一份历史报告的摘要内容...',
            },
            {
              title: '数据分析',
              content: '历史报告的数据分析结果...',
              chart: true,
            },
            {
              title: '结论',
              content: '历史报告的结论和建议...',
            },
          ]
        });
        // 设置表单值以匹配历史报告
        form.setFieldsValue({
          reportTitle: report.title,
          templateId: mockTemplates.find(t => t.name === report.template)?.id,
          // 其他字段...
        });
        message.success('历史报告加载成功');
      }, 1000);
    }
  };

  return (
    <div className="report-generation-container">
      <div className="page-header">
        <Title level={2}>报告生成</Title>
        <Paragraph>使用AI辅助快速生成专业报告，支持多种数据源和模板</Paragraph>
      </div>

      <div className="report-generation-content">
        {/* 报告生成表单 */}
        <div className="report-form-section">
          <Card>
            <div className="form-title">
              <Title level={4}>报告参数</Title>
              <Tooltip title="高级设置">
                <Button icon={<SettingOutlined />} />
              </Tooltip>
            </div>

            <Form
              form={form}
              layout="vertical"
              disabled={loading}
            >
              <div className="template-select">
                <Form.Item
                  name="templateId"
                  label="报告模板"
                  rules={[{ required: true, message: '请选择报告模板' }]}
                >
                  <Select
                    placeholder="选择报告模板"
                    onChange={handleTemplateChange}
                    loading={loading}
                  >
                    {mockTemplates.map(template => (
                      <Option key={template.id} value={template.id}>
                        {template.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Paragraph type="secondary">
                  {selectedTemplate ?
                    mockTemplates.find(t => t.id === selectedTemplate)?.description :
                    '选择一个模板开始创建报告'}
                </Paragraph>
              </div>

              <div className="data-source-section">
                <Form.Item
                  name="dataSource"
                  label="数据源"
                  rules={[{ required: true, message: '请选择数据源' }]}
                >
                  <Select
                    placeholder="选择数据源"
                    loading={loading}
                    mode="multiple"
                  >
                    {mockDataSources.map(source => (
                      <Option key={source.id} value={source.id}>
                        {source.name} ({source.type})
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  name="dateRange"
                  label="数据时间范围"
                >
                  <RangePicker style={{ width: '100%' }} />
                </Form.Item>
              </div>

              <div className="parameter-section">
                <Form.Item
                  name="reportTitle"
                  label="报告标题"
                  rules={[{ required: true, message: '请输入报告标题' }]}
                >
                  <Input placeholder="输入报告标题" />
                </Form.Item>

                <Form.Item
                  name="reportDescription"
                  label="报告说明"
                >
                  <TextArea
                    placeholder="输入报告说明或特殊要求"
                    rows={4}
                  />
                </Form.Item>
              </div>

              <div className="ai-options">
                <Title level={5}>AI辅助选项</Title>

                <div className="ai-option-item">
                  <Form.Item name="aiSummary" valuePropName="checked" noStyle>
                    <Switch defaultChecked />
                  </Form.Item>
                  <Text style={{ marginLeft: 8 }}>AI生成报告摘要</Text>
                </div>

                <div className="ai-option-item">
                  <Form.Item name="aiAnalysis" valuePropName="checked" noStyle>
                    <Switch defaultChecked />
                  </Form.Item>
                  <Text style={{ marginLeft: 8 }}>AI数据分析与解读</Text>
                </div>

                <div className="ai-option-item">
                  <Form.Item name="aiConclusion" valuePropName="checked" noStyle>
                    <Switch defaultChecked />
                  </Form.Item>
                  <Text style={{ marginLeft: 8 }}>AI生成结论与建议</Text>
                </div>

                <div className="ai-option-item">
                  <Form.Item name="aiPolish" valuePropName="checked" noStyle>
                    <Switch defaultChecked />
                  </Form.Item>
                  <Text style={{ marginLeft: 8 }}>AI语言润色与校对</Text>
                </div>
              </div>

              <div className="action-buttons">
                <Button onClick={() => form.resetFields()}>重置</Button>
                <Button
                  type="primary"
                  icon={<RobotOutlined />}
                  loading={generating}
                  onClick={handleGeneratePreview}
                >
                  AI生成预览
                </Button>
              </div>
            </Form>
          </Card>
        </div>

        {/* 报告预览 */}
        <div className="report-preview-section">
          <Card>
            <div className="preview-title">
              <Title level={4}>报告预览</Title>
              <Space>
                <Tooltip title="下载报告">
                  <Button
                    icon={<DownloadOutlined />}
                    disabled={!previewContent}
                    onClick={handleDownload}
                  />
                </Tooltip>
                <Tooltip title="打印报告">
                  <Button
                    icon={<PrinterOutlined />}
                    disabled={!previewContent}
                    onClick={handlePrint}
                  />
                </Tooltip>
                <Tooltip title="分享报告">
                  <Button
                    icon={<ShareAltOutlined />}
                    disabled={!previewContent}
                    onClick={handleShare}
                  />
                </Tooltip>
                <Tooltip title="保存报告">
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    disabled={!previewContent}
                    onClick={handleSave}
                  />
                </Tooltip>
              </Space>
            </div>

            <div className="preview-content">
              {loading ? (
                <div className="loading-state">
                  <Spin size="large" />
                  <Text style={{ marginTop: 16 }}>加载中...</Text>
                </div>
              ) : generating ? (
                <div className="loading-state">
                  <Spin size="large" />
                  <Text style={{ marginTop: 16 }}>AI正在生成报告...</Text>
                  <Text type="secondary" style={{ marginTop: 8 }}>这可能需要几秒钟时间</Text>
                </div>
              ) : !previewContent ? (
                <div className="empty-state">
                  <Empty description="暂无预览内容" />
                  <Text type="secondary" style={{ marginTop: 16 }}>
                    选择模板并点击"AI生成预览"按钮生成报告预览
                  </Text>
                </div>
              ) : (
                <div className="report-document">
                  <div className="report-header">
                    <Title>{previewContent.title}</Title>
                    <div className="report-meta">
                      <Text>生成日期: {previewContent.date}</Text>
                    </div>
                  </div>

                  <img src="http://*************:31114/tmp/zzz.png" alt="图表" style={{ width: '100%', height: '100%' }} />
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>

      {/* 历史报告 */}
      <div className="history-section">
        <div className="history-title">
          <Title level={4}>
            <HistoryOutlined /> 最近生成的报告
          </Title>
        </div>

        <List
          className="history-list"
          loading={loading}
          dataSource={mockHistoryReports}
          renderItem={item => (
            <List.Item
              className="history-item"
              actions={[
                <Button
                  key="view"
                  type="link"
                  icon={<EyeOutlined />}
                  onClick={() => handleLoadHistory(item.id)}
                >
                  查看
                </Button>
              ]}
            >
              <List.Item.Meta
                avatar={<FileTextOutlined style={{ fontSize: 24 }} />}
                title={<span className="history-item-title">{item.title}</span>}
                description={
                  <div className="history-item-meta">
                    <Space>
                      <span><ClockCircleOutlined /> {item.createTime}</span>
                      <span><UserOutlined /> {item.creator}</span>
                      <span>模板: {item.template}</span>
                    </Space>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </div>
    </div>
  );
};

export default ReportGeneration;
