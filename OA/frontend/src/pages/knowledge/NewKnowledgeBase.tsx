import React, { useState, useEffect, useRef } from 'react';
import {
  Layout,
  Typography,
  Button,
  Input,
  Upload,
  message,
  Space,
  Modal,
  Form,
  Tabs,
  Empty,
  Spin,
  Radio,
  Popconfirm,
  Tag,
  Select
} from 'antd';
import type { RadioChangeEvent } from 'antd';
import {
  UploadOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  BookOutlined,
  FileTextOutlined,
  SearchOutlined,
  LinkOutlined,
  FileAddOutlined,
  DeleteOutlined,
  EditOutlined,
  FileTextTwoTone,
  FolderOpenTwoTone,
  RobotOutlined,
  ShareAltOutlined,
  ShopOutlined,
  UserOutlined,
  StarOutlined,
  DownloadOutlined,
  EyeOutlined
} from '@ant-design/icons';
// import { useDeviceDetect } from '../../hooks/useDeviceDetect'; // 暂时未使用
import '../../styles/pages/knowledge/NewKnowledgeBase.less';
import apiClient from '../../services/apiClient';
import { getChatbotConfig } from '../../config';
import type { UploadRequestOption as RcCustomRequestOptions } from 'rc-upload/lib/interface'; // Import type for customRequest options

const { Title, Text } = Typography;
const { Search } = Input;
const { Content } = Layout;
const { TextArea } = Input;
const { TabPane } = Tabs;

// 定义知识库类型
interface KnowledgeBase {
  id: number; // OA Internal ID
  name: string;
  description: string;
  document_count: number;
  created_at: string;
  category?: string;
  external_id?: string; // <-- Use external_id for the Dify/HitoX Dataset ID
  is_shared?: boolean; // 是否已分享到市场
  shared_at?: string; // 分享时间
  author?: string; // 作者
  downloads?: number; // 下载次数
  rating?: number; // 评分
}

// 定义知识库市场项目类型
interface MarketplaceKnowledgeBase {
  id: number;
  name: string;
  description: string;
  document_count: number;
  author: string;
  created_at: string;
  shared_at: string;
  downloads: number;
  rating: number;
  tags: string[];
  category: string;
}

// 在文件顶部添加文档状态类型定义
type DocumentStatus = 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'processing' | 'completed' | 'failed';

// 修改文档接口定义
interface Document {
  id: number;
  name: string;
  description: string;
  document_type: string;
  file_size?: number;
  created_at: string;
  updated_at: string;
  status: DocumentStatus;
  knowledge_base_id: number;
}

// 定义知识库提供商类型
interface KnowledgeProvider {
  id: number;
  name: string;
  provider_type: string;
  is_active: boolean;
}

// 定义从后端获取的 Dify App 信息类型
interface DifyAppInfo {
  dify_app_id: string;
  dify_site_access_token: string;
  message: string;
}

// Define the expected structure for model_config (can be refined)
interface DifyModelConfig {
  [key: string]: any; // Allow any properties for flexibility
  model?: {
    provider: string;
    name: string;
    mode: string;
    completion_params: Record<string, any>;
  };
  dataset_configs?: {
    retrieval_model?: string;
    top_k?: number;
    score_threshold_enabled?: boolean;
    score_threshold?: number;
    reranking_enable?: boolean;
    reranking_mode?: string;
    reranking_model?: any;
    datasets?: {
      datasets?: Array<{
        dataset: {
          id: string;
          enabled?: boolean;
        }
      }>;
    };
  };
}

// 定义模型接口
interface DifyModel {
  model: string; // 模型ID，用于API请求和选择
  label: {
    zh_Hans: string;
    en_US: string;
  };
  model_type: string;
  features: string[];
  fetch_from: string;
  model_properties: {
    context_size: number;
    mode: string;
  };
  deprecated: boolean;
  status: string;
  load_balancing_enabled: boolean;
}

// 在文件顶部添加文档类型定义
interface DocumentCreate {
  name: string;
  description?: string;
  document_type: 'text' | 'file' | 'web';
  content: string;
  knowledge_base_id: number;
  doc_metadata?: Record<string, any>;
  status?: string;
}

// 添加全局缓存变量，不依赖React状态
let cachedDifyAppId: string | null = null;

const NewKnowledgeBase: React.FC = () => {
  // 状态管理
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState<KnowledgeBase | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [documentsLoading, setDocumentsLoading] = useState<boolean>(false);
  const [configuringAppLoading, setConfiguringAppLoading] = useState<boolean>(false);
  const [addContentModalVisible, setAddContentModalVisible] = useState<boolean>(false);
  const [addContentTab, setAddContentTab] = useState<string>('upload');
  const [urlInput, setUrlInput] = useState<string>('');
  const [textInput, setTextInput] = useState<string>('');
  // const { isMobile } = useDeviceDetect(); // 暂时未使用

  // 创建知识库相关状态
  const [createKBModalVisible, setCreateKBModalVisible] = useState<boolean>(false);
  const [kbName, setKbName] = useState<string>('');
  const [kbDescription, setKbDescription] = useState<string>('');
  const [createKBLoading, setCreateKBLoading] = useState<boolean>(false);
  // 默认使用Dify提供商
  const DEFAULT_PROVIDER_ID = 1; // Dify知识库提供商ID

  // --- 新增：Dify App 信息状态 ---
  const [userDifyAppId, setUserDifyAppId] = useState<string | null>(null);
  const [difySiteAccessToken, setDifySiteAccessToken] = useState<string | null>(null);
  const [tokenLoading, setTokenLoading] = useState<boolean>(true);
  const [tokenError, setTokenError] = useState<string | null>(null);
  // --- 新增：引用iframe ---
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // --- 新增：模型相关状态 ---
  const [modelList, setModelList] = useState<DifyModel[]>([]);
  const [modelLoading, setModelLoading] = useState<boolean>(false);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  // 存储当前模型配置，用于更新和显示
  const [currentModelConfig, setCurrentModelConfig] = useState<DifyModelConfig | null>(null);

  // --- 新增：知识库市场相关状态 ---
  const [currentView, setCurrentView] = useState<'personal' | 'marketplace'>('personal'); // 当前视图
  const [marketplaceKBs, setMarketplaceKBs] = useState<MarketplaceKnowledgeBase[]>([]);
  const [marketplaceLoading, setMarketplaceLoading] = useState<boolean>(false);

  // --- 新增：自定义上传请求函数 ---
  const customUploadRequest = async (options: RcCustomRequestOptions) => {
    const { onSuccess, onError, file, onProgress } = options;

    if (!selectedKnowledgeBase) {
      console.error('No knowledge base selected for upload');
      message.error('请先选择一个知识库再上传文件');
      if (onError) {
        onError(new Error('No knowledge base selected'));
      }
      return;
    }

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await apiClient.post(
        `/knowledge/bases/${selectedKnowledgeBase.id}/documents/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: (event) => {
            if (event.total && onProgress) {
              const percent = Math.round((event.loaded * 100) / event.total);
              onProgress({ percent: percent });
            }
          },
        }
      );

      if (onSuccess) {
        onSuccess(response.data); // Pass backend response data if needed
      }

      // Trigger handleUpload logic manually after successful custom request
      // We pass a simplified structure mimicking the Ant UploadChangeParam
      handleUpload({ file: { status: 'done', name: (file as File).name, response: response.data } });

    } catch (error: any) {
      console.error('上传文件出错:', error);
      if (onError) {
        onError(error);
      }
      // Trigger handleUpload logic manually for error status
      handleUpload({ file: { status: 'error', name: (file as File).name, error: error } });
    }
  };

  // 修改 fetchDifyAppInfo 函数
  const fetchDifyAppInfo = async (): Promise<{ success: boolean, appId?: string, token?: string }> => {
    setTokenLoading(true);
    setTokenError(null);
    try {
      const response = await apiClient.get<DifyAppInfo>('/dify-management/my-dify-app');
      if (response.data && response.data.dify_app_id && response.data.dify_site_access_token) {
        // 更新状态变量
        setUserDifyAppId(response.data.dify_app_id);
        setDifySiteAccessToken(response.data.dify_site_access_token);

        // 同时更新缓存变量
        cachedDifyAppId = response.data.dify_app_id;

        console.log("Successfully fetched Dify App ID:", response.data.dify_app_id);
        return {
          success: true,
          appId: response.data.dify_app_id,
          token: response.data.dify_site_access_token
        };
      } else {
        console.error('Backend response missing dify_app_id or dify_site_access_token:', response.data);
        throw new Error('未能从后端获取有效的 Dify/HitoX 应用信息 (ID 或 Token)');
      }
    } catch (error: any) {
      console.error('获取 Dify/HitoX App 信息出错:', error);
      const detail = error.response?.data?.detail || (error instanceof Error ? error.message : '获取 Dify/HitoX 应用信息失败，请稍后重试');
      message.error(`获取 Dify/HitoX 应用信息失败: ${detail}`);
      setTokenError(detail);
      return { success: false };
    } finally {
      setTokenLoading(false);
    }
  };

  // 创建知识库
  const handleCreateKnowledgeBase = async () => {
    if (!kbName.trim()) {
      message.error('请输入知识库名称');
      return;
    }

    try {
      setCreateKBLoading(true);

      await apiClient.post('/knowledge/bases', {
        name: kbName,
        description: kbDescription,
        provider_id: DEFAULT_PROVIDER_ID,
        is_public: true
      });

      message.success('知识库创建成功');

      // 重新获取知识库列表
      fetchKnowledgeBases();

      // 重置表单并关闭对话框
      setKbName('');
      setKbDescription('');
      setCreateKBModalVisible(false);
    } catch (error) {
      console.error('创建知识库出错:', error);
      message.error(`创建知识库失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setCreateKBLoading(false);
    }
  };

  // 获取知识库列表
  const fetchKnowledgeBases = async () => {
    try {
      setLoading(true);

      const response = await apiClient.get('/knowledge/bases');
      const data = response.data;

      setKnowledgeBases(data);

      // 如果有知识库，默认选择第一个
      if (data.length > 0) {
        const firstKb = data[0];
        setSelectedKnowledgeBase(firstKb);

        // 获取文档并检查是否需要绑定Dify
        const hasDocuments = await fetchDocuments(firstKb.id);

        // 如果有文档，触发Dify绑定
        if (hasDocuments) {
          await configureDifyAppForKB(firstKb, hasDocuments);
        }
      } else {
        // 如果没有知识库，清空选择和文档列表
        setSelectedKnowledgeBase(null);
        setDocuments([]);
      }
    } catch (error) {
      console.error('获取知识库列表出错:', error);
      message.error('获取知识库列表失败，请稍后重试');
      // 使用模拟数据作为后备
      const mockData = [
        {
          id: 1,
          name: '产品知识库',
          description: '包含所有产品相关文档和规格说明',
          document_count: 42,
          created_at: '2025-03-15T10:30:00Z',
          category: '产品'
        },
        {
          id: 2,
          name: '技术文档库',
          description: '技术架构、API文档和开发指南',
          document_count: 78,
          created_at: '2025-03-10T14:20:00Z',
          category: '技术'
        },
        {
          id: 3,
          name: '培训资料库',
          description: '新员工培训和技能提升课程',
          document_count: 24,
          created_at: '2025-03-05T09:15:00Z',
          category: '培训'
        }
      ];
      setKnowledgeBases(mockData);

      if (mockData.length > 0) {
        setSelectedKnowledgeBase(mockData[0]);
      }
    } finally {
      setLoading(false);
    }
  };

  // --- 新增：获取模型列表函数 ---
  const fetchModelList = async (currentModelName?: string): Promise<boolean> => {
    try {
      setModelLoading(true);
      const response = await apiClient.get('/dify-management/get-dify-models');

      if (response.data && response.data.ollama_models) {
        const models = response.data.ollama_models;
        setModelList(models);
        console.log("获取到模型列表:", models);

        // 如果传入了当前模型名称，确保它在下拉框中被选中
        if (currentModelName) {
          // 检查当前模型是否在列表中
          console.log(`检查当前模型 ${currentModelName} 是否在可用模型列表中`);
          console.log("可用模型列表:", models.map((m: DifyModel) => m.model));

          const modelExists = models.some((model: DifyModel) => model.model === currentModelName);
          if (modelExists) {
            console.log(`设置当前选中模型: ${currentModelName}`);
            setSelectedModel(currentModelName);
          } else {
            console.warn(`当前模型 ${currentModelName} 不在可用模型列表中`);
          }
        } else {
          console.log("没有传入当前模型名称");
        }

        return true;
      } else {
        console.error("获取模型列表失败: 响应格式不正确", response.data);
        message.error("获取模型列表失败");
        return false;
      }
    } catch (error: any) {
      console.error("获取模型列表出错:", error);
      const errorMsg = error.response?.data?.detail || error.message || '未知错误';
      message.error(`获取模型列表失败: ${errorMsg}`);
      return false;
    } finally {
      setModelLoading(false);
    }
  };

  // --- 新增：更新模型配置函数 ---
  const updateModelConfig = async (modelId: string): Promise<boolean> => {
    if (!userDifyAppId) {
      message.error("无法更新模型配置: 缺少应用ID");
      return false;
    }

    try {
      message.loading({ content: "正在更新模型配置...", key: "updateModel" });

      // 1. 获取当前完整配置
      const detailsResponse = await apiClient.get<{ model_config: DifyModelConfig }>(`/dify-management/get-dify-app-details/${userDifyAppId}`);
      const currentConfig = detailsResponse.data?.model_config;

      if (!currentConfig) {
        console.error('获取当前应用配置失败:', detailsResponse.data);
        throw new Error('获取当前应用配置失败');
      }

      console.log("当前模型配置:", currentConfig);

      // 2. 修改配置中的模型
      const newConfig: DifyModelConfig = JSON.parse(JSON.stringify(currentConfig)); // 深拷贝

      // 直接设置model字段的值
      newConfig.model = {
        provider: "langgenius/ollama/ollama",
        name: modelId,
        mode: "chat",
        completion_params: {}
      };

      console.log("新模型配置:", newConfig);

      // 3. 调用配置端点更新模型
      await apiClient.post(`/dify-management/configure-dify-app/${userDifyAppId}`, {
        model_config: newConfig
      });

      message.success({ content: `模型已更新为 ${modelId}`, key: "updateModel" });
      setCurrentModelConfig(newConfig);
      return true;
    } catch (error: any) {
      console.error('更新模型配置出错:', error);
      const detail = error.response?.data?.detail || (error instanceof Error ? error.message : '更新模型配置失败');
      message.error({ content: `更新模型配置失败: ${detail}`, key: "updateModel", duration: 4 });
      return false;
    }
  };

  // --- 新增：处理模型选择变更 ---
  const handleModelChange = async (modelId: string) => {
    setSelectedModel(modelId);
    await updateModelConfig(modelId);

    // 如果iframe存在，刷新iframe
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
    }
  };

  // 修改 useEffect 钩子 - 初始化
  useEffect(() => {
    // 首先获取 Dify App 信息
    const initializeApp = async () => {
      // 先获取Dify信息
      await fetchDifyAppInfo();

      // 获取知识库列表 - 无需等待Dify信息，因为我们现在使用的是缓存和直接返回值
      await fetchKnowledgeBases();

      // 获取模型列表
      await fetchModelList();
    };

    initializeApp();
  }, []);

  // 添加新的useEffect，监听userDifyAppId变化，获取当前模型配置
  useEffect(() => {
    const fetchCurrentModelConfig = async () => {
      // 获取当前模型配置
      let currentModelName: string | undefined;
      if (userDifyAppId) {
        try {
          console.log("检测到userDifyAppId变化，获取当前模型配置:", userDifyAppId);
          const detailsResponse = await apiClient.get<{ model_config: DifyModelConfig }>(`/dify-management/get-dify-app-details/${userDifyAppId}`);
          console.log("获取到的应用详情:", detailsResponse.data);

          const config = detailsResponse.data?.model_config;
          console.log("解析出的模型配置:", config);

          if (config) {
            setCurrentModelConfig(config);

            // 从model字段获取模型名称
            console.log("模型字段内容:", config.model);

            if (config.model && typeof config.model === 'object' && config.model.name) {
              currentModelName = config.model.name;
              console.log("从配置中获取到当前模型名称:", currentModelName);
              // 直接设置选中的模型，不等待模型列表加载
              setSelectedModel(currentModelName);
            } else {
              console.warn("模型配置中没有找到有效的model.name字段");
            }
          }
        } catch (error) {
          console.error("获取当前模型配置失败:", error);
        }
      }
    };

    fetchCurrentModelConfig();
  }, [userDifyAppId]);

  // 在 useState 部分添加新的状态
  const [refreshingDocStatus, setRefreshingDocStatus] = useState<Record<number, boolean>>({});
  const [documentRefreshTimers, setDocumentRefreshTimers] = useState<Record<number, ReturnType<typeof setTimeout>>>({});

  // 刷新单个文档状态
  const refreshDocumentStatus = async (doc: Document) => {
    if (!selectedKnowledgeBase || refreshingDocStatus[doc.id]) return;

    // 这里需要确保使用文档所属的知识库ID，而不是当前选中的知识库ID
    const kbId = doc.knowledge_base_id; // 使用文档自身的知识库ID

    if (!kbId) {
      console.error(`文档 ${doc.id} 没有知识库ID信息，无法刷新状态`);
      return;
    }

    try {
      setRefreshingDocStatus(prev => ({ ...prev, [doc.id]: true }));

      console.log(`请求文档详情: ID=${doc.id}, 名称=${doc.name}, 知识库ID=${kbId}`);
      const response = await apiClient.get(`/knowledge/bases/${kbId}/documents/${doc.id}`);
      const updatedDoc = response.data;
      console.log(`文档详情更新: ID=${doc.id}, 状态=${updatedDoc.status}, 知识库ID=${kbId}`);

      // 标准化状态为大写（前端使用大写）
      const normalizedStatus = updatedDoc.status?.toUpperCase() as DocumentStatus;

      // 更新文档列表中的文档状态
      setDocuments(prevDocs =>
        prevDocs.map(d =>
          d.id === doc.id ? {
            ...d,
            status: normalizedStatus || d.status,
            updated_at: updatedDoc.updated_at
          } : d
        )
      );

      // 如果文档状态为处理中，设置定时器继续检查
      if (normalizedStatus === 'PROCESSING' || updatedDoc.status === 'processing') {
        // 清除旧定时器
        if (documentRefreshTimers[doc.id]) {
          clearTimeout(documentRefreshTimers[doc.id]);
        }

        // 设置新定时器，10秒后再次检查
        const timerId = setTimeout(() => refreshDocumentStatus(doc), 10000);
        setDocumentRefreshTimers(prev => ({ ...prev, [doc.id]: timerId }));
      }
    } catch (error) {
      console.error(`获取文档详情出错: ${doc.id}, 知识库ID=${kbId}`, error);
    } finally {
      setRefreshingDocStatus(prev => ({ ...prev, [doc.id]: false }));
    }
  };

  // 添加更新知识库文档数量的函数
  const updateKnowledgeBaseDocumentCount = (kbId: number, count?: number) => {
    setKnowledgeBases(prevKBs =>
      prevKBs.map(kb =>
        kb.id === kbId ?
          {
            ...kb,
            document_count: count !== undefined ? count : kb.document_count
          } :
          kb
      )
    );
  };

  // 修改fetchDocuments函数，更新文档数量
  const fetchDocuments = async (kbId: number): Promise<boolean> => {
    setDocumentsLoading(true);
    setDocuments([]);
    try {
      const response = await apiClient.get<Document[]>(`/knowledge/bases/${kbId}/documents`);
      const data = response.data;
      setDocuments(data);

      // 更新知识库的文档数量
      updateKnowledgeBaseDocumentCount(kbId, data.length);

      // 清除所有现有的定时器
      Object.values(documentRefreshTimers).forEach(timer => clearTimeout(timer));
      setDocumentRefreshTimers({});

      // 对所有文档发起详情请求
      console.log(`获取到${data.length}个文档，开始请求每个文档的详情`);

      // 使用延迟错开请求，避免同时发送太多请求
      data.forEach((doc, index) => {
        setTimeout(() => {
          refreshDocumentStatus(doc);
        }, index * 300); // 每个请求间隔300毫秒
      });

      return data && data.length > 0;
    } catch (error) {
      console.error('获取文档列表出错:', error);
      message.error('获取文档列表失败，请稍后重试');
      setDocuments([]);
      return false;
    } finally {
      setDocumentsLoading(false);
    }
  };

  // 修改 configureDifyAppForKB 函数
  const configureDifyAppForKB = async (kb: KnowledgeBase | null, hasDocuments: boolean) => {
    if (!kb) {
      console.warn('Skipping Dify app configuration: No KB selected.');
      return;
    }

    // 尝试获取 Dify App ID (优先使用缓存)
    let difyAppId = cachedDifyAppId || userDifyAppId;

    // 如果没有缓存和状态中的ID，直接重新请求
    if (!difyAppId) {
      console.log('No Dify App ID available, fetching...');
      const fetchResult = await fetchDifyAppInfo();
      if (fetchResult.success && fetchResult.appId) {
        difyAppId = fetchResult.appId;
        console.log('Successfully fetched Dify App ID:', difyAppId);
      } else {
        console.error('Failed to get Dify App ID, aborting configuration.');
        message.error('无法获取Dify应用信息，无法配置知识库绑定');
        return;
      }
    }

    console.log(`Using Dify App ID for configuration: ${difyAppId}`);
    setConfiguringAppLoading(true);
    message.loading({ content: `正在为应用配置知识库 ${kb?.name}...`, key: 'configureApp' });

    try {
      // 1. Get current full config
      const detailsResponse = await apiClient.get<{ model_config: DifyModelConfig }>(`/dify-management/get-dify-app-details/${difyAppId}`);
      const currentConfig = detailsResponse.data?.model_config;

      // Check if model_config exists in the response
      if (!currentConfig) {
        console.error('Failed to get current app model config from response:', detailsResponse.data);
        throw new Error('获取当前应用配置失败，无法继续配置知识库。');
      }

      console.log("Current model config fetched:", currentConfig);

      // 2. Modify the config based on document presence
      const newConfig: DifyModelConfig = JSON.parse(JSON.stringify(currentConfig)); // Deep copy

      // 确保保留当前选中的模型配置
      if (selectedModel && (!newConfig.model || newConfig.model.name !== selectedModel)) {
        console.log(`保留当前选中的模型配置: ${selectedModel}`);
        newConfig.model = {
          provider: "langgenius/ollama/ollama",
          name: selectedModel,
          mode: "chat",
          completion_params: {}
        };
      }

      // Ensure dataset_configs structure exists
      if (!newConfig.dataset_configs) {
        newConfig.dataset_configs = {};
      }
      if (!newConfig.dataset_configs.datasets) {
        newConfig.dataset_configs.datasets = { datasets: [] };
      }

      if (hasDocuments) {
        if (!kb.external_id) {
          console.error(`知识库 ${kb?.name} (OA ID: ${kb?.id}) 缺少对应的 Dify/HitoX Dataset ID (external_id)，无法绑定。`);
          throw new Error(`知识库 ${kb?.name} 配置信息不完整 (缺少 external_id)，无法绑定。`);
        }
        console.log(`Binding KB ${kb.external_id} (OA ID: ${kb.id}) to app ${difyAppId}`);
        newConfig.dataset_configs.datasets.datasets = [
          {
            dataset: {
              enabled: true,
              id: kb.external_id
            }
          }
        ];
      } else {
        console.log(`Unbinding all KBs from app ${difyAppId} because KB ${kb?.id} has no documents`);
        newConfig.dataset_configs.datasets.datasets = [];
      }

      console.log("New model config prepared:", newConfig);

      // 3. Call the configure endpoint with the complete modified config
      await apiClient.post(`/dify-management/configure-dify-app/${difyAppId}`, {
        model_config: newConfig
      });

      message.success({ content: `知识库 ${kb?.name} 配置成功！`, key: 'configureApp', duration: 2 });

    } catch (error: any) {
      console.error('配置 Dify/HitoX 应用知识库出错:', error);
      const detail = error.response?.data?.detail || (error instanceof Error ? error.message : '配置应用知识库失败');
      message.error({ content: `配置知识库失败: ${detail}`, key: 'configureApp', duration: 4 });
    } finally {
      setConfiguringAppLoading(false);
    }
  };

  // 处理知识库选择
  const handleKnowledgeBaseSelect = async (kb: KnowledgeBase) => {
    if (selectedKnowledgeBase?.id === kb.id) return;

    setSelectedKnowledgeBase(kb);
    setSelectedDocument(null);

    // 获取文档并检查是否有文档
    const hasDocuments = await fetchDocuments(kb.id);

    // 总是尝试配置Dify App，无论是否有文档
    // 有文档时绑定，无文档时解绑
    await configureDifyAppForKB(kb, hasDocuments);
  };

  // 处理文档选择
  const handleDocumentSelect = (doc: Document) => {
    setSelectedDocument(doc);
  };

  // 处理添加内容
  const handleAddContent = () => {
    setAddContentModalVisible(true);
    setAddContentTab('upload');
  };

  // 处理上传文件
  const handleUpload = (info: any) => {
    if (info.file.status === 'done') {
      message.success(`${info.file.name} 上传成功`);
      // 刷新文档列表
      if (selectedKnowledgeBase) {
        fetchDocuments(selectedKnowledgeBase.id).then(hasDocuments => {
          // 重新配置 Dify App，绑定知识库
          configureDifyAppForKB(selectedKnowledgeBase, hasDocuments);

          // 尝试找到刚上传的文档并刷新其状态
          const newDoc = documents.find(d => d.name.includes(info.file.name));
          if (newDoc && newDoc.status === 'PROCESSING') {
            refreshDocumentStatus(newDoc);
          }
        });
      }
      setAddContentModalVisible(false);
    } else if (info.file.status === 'error') {
      // Extract more specific error from backend if available
      const errorMsg = info.file.error?.response?.data?.detail || info.file.error?.message || '上传失败';
      message.error(`${info.file.name} 上传失败: ${errorMsg}`);
    }
  };

  // 处理添加URL
  const handleAddUrl = () => {
    if (!urlInput.trim()) {
      message.error('请输入有效的URL');
      return;
    }

    message.success('URL已添加，正在处理...');
    setUrlInput('');
    setAddContentModalVisible(false);
  };

  // 处理添加文本
  const handleAddText = async () => {
    if (!textInput.trim()) {
      message.error('请输入文本内容');
      return;
    }

    if (!selectedKnowledgeBase) {
      message.error('请先选择一个知识库');
      return;
    }

    try {
      message.loading({ content: '正在添加文本...', key: 'addText' });

      // 准备符合后端要求的文档数据
      const docData: DocumentCreate = {
        name: `文本文档-${new Date().toLocaleString('zh-CN', { hour12: false })}`,
        description: `于 ${new Date().toLocaleString('zh-CN', { hour12: false })} 创建的文本文档`,
        document_type: 'text',
        content: textInput,
        knowledge_base_id: selectedKnowledgeBase.id,
        doc_metadata: {
          source: 'manual_input',
          created_at: new Date().toISOString()
        }
      };

      // 调用API创建文本文档
      await apiClient.post(
        `/knowledge/bases/${selectedKnowledgeBase.id}/documents`,
        docData
      );

      message.success({ content: '文本已添加成功!', key: 'addText' });

      // 清空输入框并关闭对话框
      setTextInput('');
      setAddContentModalVisible(false);

      // 刷新文档列表
      const hasDocuments = await fetchDocuments(selectedKnowledgeBase.id);
      // 重新配置 Dify App，绑定知识库
      await configureDifyAppForKB(selectedKnowledgeBase, hasDocuments);

    } catch (error: any) {
      console.error('添加文本出错:', error);
      const errorMsg = error.response?.data?.detail || error.message || '未知错误';
      message.error({ content: `添加文本失败: ${errorMsg}`, key: 'addText', duration: 5 });
    }
  };

  // 处理文档删除
  const handleDeleteDocument = async (doc: Document) => {
    if (!selectedKnowledgeBase) return;

    try {
      await apiClient.delete(`/knowledge/bases/${selectedKnowledgeBase.id}/documents/${doc.id}`);
      message.success(`${doc.name} 删除成功`);

      // 从当前文档列表中移除已删除的文档
      const updatedDocs = documents.filter(d => d.id !== doc.id);
      setDocuments(updatedDocs);

      // 直接更新知识库的文档数量
      updateKnowledgeBaseDocumentCount(selectedKnowledgeBase.id, updatedDocs.length);

      // 重新配置 Dify App，更新知识库绑定状态
      await configureDifyAppForKB(selectedKnowledgeBase, updatedDocs.length > 0);
    } catch (error) {
      console.error('删除文档出错:', error);
      message.error(`删除文档失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 添加删除知识库的处理函数
  const handleDeleteKnowledgeBase = async (kb: KnowledgeBase, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发选择知识库

    try {
      message.loading({ content: '正在删除知识库...', key: 'deleteKB' });

      await apiClient.delete(`/knowledge/bases/${kb.id}`);

      message.success({ content: `知识库 "${kb.name}" 已删除`, key: 'deleteKB' });

      // 重新获取知识库列表
      const response = await apiClient.get('/knowledge/bases');
      const kbList = response.data;
      setKnowledgeBases(kbList);

      // 如果删除的是当前选中的知识库，选择新的知识库
      if (selectedKnowledgeBase?.id === kb.id) {
        if (kbList.length > 0) {
          const newSelectedKb = kbList[0];
          setSelectedKnowledgeBase(newSelectedKb);

          // 获取新选中知识库的文档并检查是否需要绑定Dify
          const hasDocuments = await fetchDocuments(newSelectedKb.id);

          // 如果有文档，触发Dify绑定
          if (hasDocuments) {
            await configureDifyAppForKB(newSelectedKb, hasDocuments);
          }
        } else {
          // 如果没有知识库了，清空选择和文档列表
          setSelectedKnowledgeBase(null);
          setDocuments([]);
        }
      }
    } catch (error: any) {
      console.error('删除知识库出错:', error);
      const errorMsg = error.response?.data?.detail || error.message || '未知错误';
      message.error({ content: `删除知识库失败: ${errorMsg}`, key: 'deleteKB', duration: 5 });
    }
  };

  // --- 新增：分享知识库函数 ---
  const handleShareKnowledgeBase = async (kb: KnowledgeBase, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡

    try {
      message.loading({ content: '正在分享知识库...', key: 'shareKB' });

      // 这里可以调用后端API来分享知识库
      // await apiClient.post(`/knowledge/bases/${kb.id}/share`);

      // 暂时使用模拟的分享逻辑
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用

      message.success({ content: `知识库 "${kb.name}" 已成功分享到市场！`, key: 'shareKB' });

      // 更新知识库状态为已分享
      setKnowledgeBases(prevKBs =>
        prevKBs.map(prevKb =>
          prevKb.id === kb.id
            ? { ...prevKb, is_shared: true, shared_at: new Date().toISOString() }
            : prevKb
        )
      );

      // 添加到市场数据中
      const newMarketplaceKB: MarketplaceKnowledgeBase = {
        id: kb.id,
        name: kb.name,
        description: kb.description,
        document_count: kb.document_count,
        author: '当前用户', // 实际应用中应该从用户信息获取
        created_at: kb.created_at,
        shared_at: new Date().toISOString(),
        downloads: 0,
        rating: 5.0,
        tags: ['个人分享', kb.category || '未分类'],
        category: kb.category || '未分类'
      };

      setMarketplaceKBs(prev => [newMarketplaceKB, ...prev]);

    } catch (error: any) {
      console.error('分享知识库出错:', error);
      const errorMsg = error.response?.data?.detail || error.message || '未知错误';
      message.error({ content: `分享知识库失败: ${errorMsg}`, key: 'shareKB', duration: 5 });
    }
  };

  // --- 新增：获取知识库市场数据 ---
  const fetchMarketplaceKnowledgeBases = async () => {
    try {
      setMarketplaceLoading(true);

      // 这里可以调用后端API获取市场数据
      // const response = await apiClient.get('/knowledge/marketplace');
      // setMarketplaceKBs(response.data);

      // 暂时使用模拟数据
      const mockMarketplaceData: MarketplaceKnowledgeBase[] = [
        {
          id: 1001,
          name: '企业管理制度大全',
          description: '包含人事、财务、行政等各类企业管理制度模板，适用于中小企业快速建立规范化管理体系',
          document_count: 156,
          author: '管理专家张三',
          created_at: '2024-01-15T10:30:00Z',
          shared_at: '2024-01-20T14:20:00Z',
          downloads: 2847,
          rating: 4.8,
          tags: ['企业管理', '制度模板', '人事', '财务'],
          category: '企业管理'
        },
        {
          id: 1002,
          name: '技术文档写作指南',
          description: '涵盖API文档、用户手册、技术规范等各类技术文档的写作方法和最佳实践',
          document_count: 89,
          author: '技术写作专家李四',
          created_at: '2024-02-01T09:15:00Z',
          shared_at: '2024-02-05T16:45:00Z',
          downloads: 1923,
          rating: 4.9,
          tags: ['技术写作', 'API文档', '用户手册'],
          category: '技术文档'
        },
        {
          id: 1003,
          name: '市场营销策略库',
          description: '收录了各行业成功的营销案例、策略模板和实战经验，助力企业提升营销效果',
          document_count: 234,
          author: '营销总监王五',
          created_at: '2024-01-28T11:20:00Z',
          shared_at: '2024-02-02T13:30:00Z',
          downloads: 3156,
          rating: 4.7,
          tags: ['市场营销', '营销策略', '案例分析'],
          category: '市场营销'
        },
        {
          id: 1004,
          name: '法律法规汇编',
          description: '整理了企业经营相关的法律法规、合同模板和法务实务指南',
          document_count: 178,
          author: '法务专员赵六',
          created_at: '2024-01-10T14:45:00Z',
          shared_at: '2024-01-18T10:15:00Z',
          downloads: 1567,
          rating: 4.6,
          tags: ['法律法规', '合同模板', '法务'],
          category: '法律事务'
        },
        {
          id: 1005,
          name: '产品设计资源库',
          description: '包含UI/UX设计规范、原型模板、设计工具使用指南等产品设计相关资源',
          document_count: 312,
          author: '设计师小明',
          created_at: '2024-02-10T16:30:00Z',
          shared_at: '2024-02-15T09:45:00Z',
          downloads: 2234,
          rating: 4.9,
          tags: ['产品设计', 'UI设计', 'UX设计', '原型'],
          category: '产品设计'
        }
      ];

      setMarketplaceKBs(mockMarketplaceData);

    } catch (error: any) {
      console.error('获取知识库市场数据出错:', error);
      message.error('获取知识库市场数据失败，请稍后重试');
    } finally {
      setMarketplaceLoading(false);
    }
  };

  // 渲染知识库分类
  const renderKnowledgeCategories = () => {
    // 按类别分组知识库
    const categorizedKBs: Record<string, KnowledgeBase[]> = {};

    knowledgeBases.forEach(kb => {
      const category = kb.category || '个人知识库'; // 修改：将"未分类"改为"个人知识库"
      if (!categorizedKBs[category]) {
        categorizedKBs[category] = [];
      }
      categorizedKBs[category].push(kb);
    });

    return Object.entries(categorizedKBs).map(([category, kbs]) => (
      <div className="knowledge-category" key={category}>
        <div className="category-title">{category}</div>
        {kbs.map(kb => (
          <div
            key={kb.id}
            className={`knowledge-item ${selectedKnowledgeBase?.id === kb.id ? 'active' : ''}`}
          >
            <div
              className="kb-content"
              onClick={() => handleKnowledgeBaseSelect(kb)}
            >
              <BookOutlined className="item-icon" />
              <span className="kb-name">{kb.name}</span>
              <span className="item-count">{kb.document_count}</span>
            </div>
            <div className="kb-actions">
              <Space size={4}>
                {/* 新增：分享按钮 */}
                <Button
                  type="text"
                  size="small"
                  icon={<ShareAltOutlined />}
                  onClick={(e) => handleShareKnowledgeBase(kb, e)}
                  title={kb.is_shared ? "已分享到市场" : "分享到知识库市场"}
                  style={{
                    color: kb.is_shared ? '#52c41a' : '#1890ff'
                  }}
                />
                <Popconfirm
                  title="确定要删除这个知识库吗？"
                  description={
                    <div>
                      <p>删除后将无法恢复，知识库中的所有文档也会被删除。</p>
                      <p>请谨慎操作。</p>
                    </div>
                  }
                  onConfirm={(e) => handleDeleteKnowledgeBase(kb, e as React.MouseEvent)}
                  okText="确定"
                  cancelText="取消"
                  placement="right"
                >
                  <Button
                    type="text"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={(e) => e.stopPropagation()}
                  />
                </Popconfirm>
              </Space>
            </div>
          </div>
        ))}
      </div>
    ));
  };

  // --- 新增：渲染知识库市场 ---
  const renderMarketplace = () => {
    if (marketplaceLoading) {
      return (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin tip="加载知识库市场中..." />
        </div>
      );
    }

    return (
      <div className="marketplace-container">
        <div className="marketplace-header">
          <Title level={3}>
            <ShopOutlined style={{ marginRight: 8 }} />
            知识库市场
          </Title>
          <Text type="secondary">
            发现和下载优质的知识库资源，提升工作效率
          </Text>
        </div>

        <div className="marketplace-content">
          {marketplaceKBs.length === 0 ? (
            <Empty
              description="暂无分享的知识库"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ) : (
            <div className="marketplace-grid">
              {marketplaceKBs.map(kb => (
                <div key={kb.id} className="marketplace-item">
                  <div className="marketplace-item-header">
                    <Title level={5} style={{ margin: 0 }}>
                      <BookOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                      {kb.name}
                    </Title>
                    <div className="marketplace-item-meta">
                      <Space size={8}>
                        <Tag color="blue">{kb.category}</Tag>
                        <span className="rating">
                          <StarOutlined style={{ color: '#faad14' }} />
                          {kb.rating}
                        </span>
                      </Space>
                    </div>
                  </div>

                  <div className="marketplace-item-content">
                    <Text type="secondary" className="description">
                      {kb.description}
                    </Text>

                    <div className="marketplace-item-stats">
                      <Space size={16}>
                        <span>
                          <FileTextOutlined style={{ marginRight: 4 }} />
                          {kb.document_count} 文档
                        </span>
                        <span>
                          <DownloadOutlined style={{ marginRight: 4 }} />
                          {kb.downloads} 下载
                        </span>
                        <span>
                          <UserOutlined style={{ marginRight: 4 }} />
                          {kb.author}
                        </span>
                      </Space>
                    </div>

                    <div className="marketplace-item-tags">
                      {kb.tags.map(tag => (
                        <Tag key={tag}>{tag}</Tag>
                      ))}
                    </div>
                  </div>

                  <div className="marketplace-item-footer">
                    <Space>
                      <Button
                        type="primary"
                        size="small"
                        icon={<DownloadOutlined />}
                        onClick={() => {
                          message.success(`开始下载知识库: ${kb.name}`);
                          // 这里可以实现实际的下载逻辑
                        }}
                      >
                        下载
                      </Button>
                      <Button
                        size="small"
                        icon={<EyeOutlined />}
                        onClick={() => {
                          message.info(`查看知识库详情: ${kb.name}`);
                          // 这里可以实现查看详情的逻辑
                        }}
                      >
                        详情
                      </Button>
                    </Space>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      分享于 {new Date(kb.shared_at).toLocaleDateString()}
                    </Text>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  // 渲染文档列表
  const renderDocumentList = () => {
    if (documentsLoading) {
      return (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin tip="加载文档中..." />
        </div>
      );
    }

    if (documents.length === 0 && selectedKnowledgeBase) {
      return (
        <div className="empty-state">
          <Title level={4} style={{ marginBottom: 16 }}>添加内容到知识库</Title>
          <Text type="secondary" style={{ marginBottom: 24, display: 'block' }}>
            您可以通过以下方式向知识库添加内容
          </Text>

          <div className="upload-area">
            <UploadOutlined className="upload-icon" />
            <Title level={4}>拖入文件或点击上传</Title>
            <Text type="secondary">
              支持文档、表格、图片、视频等多种格式，单个文件最大50MB
            </Text>
            <Upload
              name="file"
              customRequest={customUploadRequest}
              showUploadList={false}
            >
              <Button type="primary" icon={<UploadOutlined />} style={{ marginTop: 16 }}>
                上传文件
              </Button>
            </Upload>
          </div>

          <Title level={5} style={{ marginTop: 8, marginBottom: 16 }}>其他添加方式</Title>
          <div className="action-buttons">
            <Button icon={<LinkOutlined />} onClick={() => {
              setAddContentModalVisible(true);
              setAddContentTab('url');
            }}>
              添加网址
            </Button>
            <Button icon={<FileTextOutlined />} onClick={() => {
              setAddContentModalVisible(true);
              setAddContentTab('text');
            }}>
              粘贴文字
            </Button>
          </div>
        </div>
      );
    } else if (!selectedKnowledgeBase) {
      return <Empty description="请先选择或创建一个知识库" />;
    }

    return (
      <div className="document-list">
        {documents.map(doc => {
          // 标准化状态显示（处理大小写问题）
          const status = (doc.status || '').toLowerCase();

          return (
            <div
              key={doc.id}
              className={`document-item ${selectedDocument?.id === doc.id ? 'active' : ''}`}
            >
              <div
                className="doc-item-content"
                onClick={() => handleDocumentSelect(doc)}
              >
                <div className="document-title">
                  <FileTextTwoTone style={{ marginRight: 8, fontSize: '16px', flexShrink: 0 }} />
                  <span className="doc-name">{doc.name}</span>
                </div>
                <div className="document-meta">
                  <Space size={4}>
                    <Tag color="blue" style={{ margin: 0 }}>{doc.document_type.toUpperCase()}</Tag>
                    <span style={{ color: '#8c8c8c' }}>{new Date(doc.updated_at).toLocaleDateString()}</span>
                    {status === 'processing' && (
                      <Tag color="orange" style={{ margin: 0 }}>
                        <span className="processing-text">
                          处理中
                          <span className="processing-dots">
                            <span className="dot">.</span>
                            <span className="dot">.</span>
                            <span className="dot">.</span>
                          </span>
                        </span>
                        <Spin size="small" style={{ marginLeft: 5 }} />
                      </Tag>
                    )}
                    {status === 'completed' && (
                      <Tag color="green" style={{ margin: 0 }}>已完成</Tag>
                    )}
                    {status === 'failed' && (
                      <Tag color="red" style={{ margin: 0 }}>处理失败</Tag>
                    )}
                  </Space>
                </div>
              </div>
              <div className="document-actions">
                <Space>
                  <Popconfirm
                    title="确定要删除这个文档吗？"
                    description="删除后将无法恢复，请谨慎操作。"
                    onConfirm={(e) => {
                      e?.stopPropagation();
                      handleDeleteDocument(doc);
                    }}
                    okText="确定"
                    cancelText="取消"
                    placement="left"
                  >
                    <Button
                      type="text"
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Popconfirm>
                </Space>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // --- 新增：渲染 Dify Chatbot Iframe 或加载/错误状态 ---
  const renderDifyChatbot = () => {
    if (tokenLoading) {
      return (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
          <Spin tip="加载 助手 Token..." />
        </div>
      );
    }

    if (tokenError) {
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <Text type="danger">加载 助手失败:</Text>
          <Text type="secondary" style={{ display: 'block', marginTop: '8px' }}>{tokenError}</Text>
          <Button onClick={fetchDifyAppInfo} style={{ marginTop: '16px' }}>重试</Button>
        </div>
      );
    }

    if (difySiteAccessToken) {
      // 从配置中获取聊天机器人URL模板
      const chatbotConfig = getChatbotConfig();
      const iframeSrc = chatbotConfig.urlTemplate.replace('${token}', difySiteAccessToken);
      console.log("Rendering iframe with src:", iframeSrc); // Debug log

      return (
        <div className="chatbot-container">
          {/* 模型选择下拉框 */}
          <div className="model-selector">
            <Space align="center">
              <RobotOutlined style={{ fontSize: '16px' }} />
              <Text strong>模型选择:</Text>
              <Select
                loading={modelLoading}
                value={selectedModel}
                onChange={handleModelChange}
                style={{ width: 240 }}
                placeholder="选择模型"
                disabled={modelLoading || modelList.length === 0}
                onDropdownVisibleChange={(open) => {
                  if (open) {
                    console.log("下拉框打开，当前选中模型:", selectedModel);
                    console.log("可用模型列表:", modelList);
                  }
                }}
                showSearch
                optionFilterProp="children"
              >
                {modelList.map(model => (
                  <Select.Option key={model.model} value={model.model}>
                    {model.label.zh_Hans || model.model}
                  </Select.Option>
                ))}
              </Select>
            </Space>
          </div>

          {/* iframe */}
          <iframe
            ref={iframeRef} // Assign ref
            src={iframeSrc}
            style={{ width: '100%', height: 'calc(100% - 50px)', border: 'none', minHeight: '650px' }} // 调整高度以适应模型选择器
            allow="microphone"
            title="Dify Chatbot Assistant" // Added title for accessibility
          ></iframe>
        </div>
      );
    }

    // Fallback if token is null without error (shouldn't happen with current logic)
    return <Empty description="未能获取 Dify 助手 Token" />;
  };

  // 替换掉监听documents变化的useEffect
  useEffect(() => {
    // 清理函数
    return () => {
      Object.values(documentRefreshTimers).forEach(timer => clearTimeout(timer));
    };
  }, []);

  // 添加调试useEffect，监控selectedModel的变化
  useEffect(() => {
    console.log("selectedModel发生变化:", selectedModel);
  }, [selectedModel]);

  return (
    <div className="new-knowledge-base-container">
      {/* 左侧知识库导航栏 */}
      <div className="knowledge-sidebar">
        <div className="sidebar-header">
          <Title level={4}>知识库</Title>
          <Search placeholder="搜索知识库" size="small" style={{ marginTop: 8 }} />
        </div>

        {/* 新增：导航菜单 */}
        <div className="navigation-menu">
          <div
            className={`nav-item ${currentView === 'personal' ? 'active' : ''}`}
            onClick={() => setCurrentView('personal')}
          >
            <UserOutlined className="nav-icon" />
            <span>个人知识库</span>
          </div>
          <div
            className={`nav-item ${currentView === 'marketplace' ? 'active' : ''}`}
            onClick={() => {
              setCurrentView('marketplace');
              if (marketplaceKBs.length === 0) {
                fetchMarketplaceKnowledgeBases();
              }
            }}
          >
            <ShopOutlined className="nav-icon" />
            <span>知识库市场</span>
          </div>
        </div>

        {/* 只在个人知识库视图下显示知识库列表和创建按钮 */}
        {currentView === 'personal' && (
          <>
            {loading ? (
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <Spin tip="加载中..." />
              </div>
            ) : (
              renderKnowledgeCategories()
            )}
            <Button
              type="primary"
              icon={<PlusOutlined />}
              className="create-button"
              onClick={() => setCreateKBModalVisible(true)}
            >
              创建知识库
            </Button>
          </>
        )}
      </div>

      {/* 中间内容区域 - 根据当前视图显示不同内容 */}
      {currentView === 'personal' ? (
        <>
          {/* 个人知识库视图 - 中间文档区域 */}
          <div className="document-content">
            {configuringAppLoading && <div style={{ position: 'absolute', top: 10, right: 10, zIndex: 10 }}><Spin size="small" /></div>}
            <div className="content-header">
              <Title level={4}>
                {selectedKnowledgeBase ? selectedKnowledgeBase.name : '选择知识库'}
              </Title>
              <Space>
                <Button icon={<SearchOutlined />}>搜索</Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddContent}
                  disabled={!selectedKnowledgeBase}
                >
                  添加内容
                </Button>
              </Space>
            </div>
            <div className="document-list-container">
              {renderDocumentList()}
            </div>
          </div>

          {/* 右侧AI聊天区域 */}
          <div className="ai-chat dify-chatbot-container">
            {renderDifyChatbot()}
          </div>
        </>
      ) : (
        /* 知识库市场视图 - 合并中栏和右栏显示市场内容 */
        <div className="marketplace-full-content">
          {renderMarketplace()}
        </div>
      )}

      {/* 添加内容模态框 */}
      <Modal
        title="添加内容"
        open={addContentModalVisible}
        onCancel={() => setAddContentModalVisible(false)}
        footer={null}
      >
        <Tabs activeKey={addContentTab} onChange={setAddContentTab}>
          <TabPane tab="上传文件" key="upload">
            <Upload
              name="file"
              customRequest={customUploadRequest}
              multiple
              showUploadList={true}
            >
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>
            <Text type="secondary" style={{ display: 'block', marginTop: 16 }}>
              支持文档、表格、图片、视频等多种格式，单个文件最大50MB
            </Text>
          </TabPane>
          <TabPane tab="添加网址" key="url">
            <Input
              placeholder="输入网址"
              value={urlInput}
              onChange={e => setUrlInput(e.target.value)}
              style={{ marginBottom: 16 }}
            />
            <Button type="primary" onClick={handleAddUrl}>添加</Button>
          </TabPane>
          <TabPane tab="粘贴文字" key="text">
            <TextArea
              placeholder="请粘贴或输入要添加到知识库的文本内容"
              rows={10}
              value={textInput}
              onChange={e => setTextInput(e.target.value)}
              style={{ marginBottom: 16 }}
              showCount
              maxLength={50000}
            />
            <Space>
              <Button type="primary" onClick={handleAddText}>添加到知识库</Button>
              <Text type="secondary">支持最多50,000个字符</Text>
            </Space>
          </TabPane>
        </Tabs>
      </Modal>

      {/* 创建知识库模态框 */}
      <Modal
        title="创建知识库"
        open={createKBModalVisible}
        onCancel={() => setCreateKBModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setCreateKBModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={createKBLoading}
            onClick={handleCreateKnowledgeBase}
          >
            创建
          </Button>
        ]}
      >
        <Form layout="vertical">
          <Form.Item
            label="知识库名称"
            required
            tooltip="请输入知识库名称，例如：产品知识库、技术文档库等"
          >
            <Input
              placeholder="请输入知识库名称"
              value={kbName}
              onChange={(e) => setKbName(e.target.value)}
            />
          </Form.Item>
          <Form.Item
            label="知识库描述"
            tooltip="简要描述该知识库的用途和内容"
          >
            <TextArea
              placeholder="请输入知识库描述"
              rows={4}
              value={kbDescription}
              onChange={(e) => setKbDescription(e.target.value)}
            />
          </Form.Item>

        </Form>
      </Modal>
    </div>
  );
};

export default NewKnowledgeBase;

