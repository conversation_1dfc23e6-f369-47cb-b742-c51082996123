import React, { useState, useRef, useEffect } from 'react';
import { Card, Steps, Button, Input, Upload, message, Tooltip, Alert, Progress, Result, DatePicker, Table, Tag, List, Timeline } from 'antd';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { 
  RobotOutlined, 
  AudioOutlined, 
  FormOutlined, 
  SendOutlined, 
  UploadOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  QuestionCircleOutlined,
  WarningOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import '../../styles/pages/ai-assistant/AIGuide.less';
import apiClient from '../../services/apiClient';

// 导入自定义类型和模板工具
import { FormField, MissingField, FormSchema, AnalysisResult, RefinementResult, ProcessTemplate } from '../../types/process';
import { calculateMissingFields, extractFormFields } from '../../utils/processTemplates';

// 配置dayjs插件
dayjs.extend(utc);
dayjs.extend(timezone);

const { Step } = Steps;
const { TextArea } = Input;

const AIGuide: React.FC = () => {
  // 基本状态
  const [currentStep, setCurrentStep] = useState(0);
  const [inputValue, setInputValue] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // 表单相关状态
  const [formData, setFormData] = useState<Record<string, any>>({});
  // formSchema用于存储表单的结构定义，可用于动态渲染表单
  const [formSchema, setFormSchema] = useState<FormSchema | null>(null);
  const [missingFields, setMissingFields] = useState<MissingField[]>([]);
  const [requiredFields, setRequiredFields] = useState<MissingField[]>([]);
  const [templateId, setTemplateId] = useState<number | null>(null);
  const [templateName, setTemplateName] = useState<string>('');
  const [processType, setProcessType] = useState<string>('');
  const [department, setDepartment] = useState<string>('');
  const [confidence, setConfidence] = useState<number>(0);
  // 存储所有流程模板
  const [processTemplates, setProcessTemplates] = useState<ProcessTemplate[]>([]);
  
  // 补充描述状态
  const [supplementText, setSupplementText] = useState<string>('');
  
  // 流程结果状态
  const [processResult, setProcessResult] = useState<any>(null);
  
  // 用户信息状态
  const [userInfo, setUserInfo] = useState<{full_name?: string; department?: string; position?: string; email?: string}>({});

  // 添加流程详情相关状态
  const [showProcessDetail, setShowProcessDetail] = useState(false);
  const [processDetail, setProcessDetail] = useState<any>(null);
  const [detailLoading, setDetailLoading] = useState(false);

  // 添加缺失的状态变量
  const [refinementLoading, setRefinementLoading] = useState<boolean>(false);
  const [refinementError, setRefinementError] = useState<string | null>(null);
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<boolean>(false);
  const [processId, setProcessId] = useState<number | null>(null);
  const [processLink, setProcessLink] = useState<string>('');

  // 获取当前用户信息
  const getCurrentUserInfo = async () => {
    try {
      // 使用apiClient替代fetch
      const response = await apiClient.get('/users/me');
      setUserInfo(response.data);
      // 如果有部门信息，自动设置到表单中
      if (response.data.department) {
        setDepartment(response.data.department);
      }
    } catch (err) {
      console.error('获取用户信息出错:', err);
      // 出错时使用模拟数据
      useMockUserData();
    }
  };
  
  // 模拟用户数据，仅在开发阶段使用
  const useMockUserData = () => {
    setUserInfo({
      full_name: '张三',
      department: '技术部',
      position: '高级开发工程师',
      email: '<EMAIL>'
    });
    setDepartment('技术部');
  };
  
  // 将样式定义添加到组件渲染中使用
  React.useEffect(() => {
    // 添加自定义样式到head
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      .form-row.missing-field .form-label {
        color: #ff4d4f;
        font-weight: bold;
      }
      .required-mark {
        color: #ff4d4f;
        margin-left: 4px;
      }
      .field-error {
        color: #ff4d4f;
        padding: 2px 8px;
        border: 1px solid #ff4d4f;
        border-radius: 2px;
        margin-top: 4px;
        display: inline-block;
        background-color: #fff2f0;
      }
      .error-input, .ant-input-status-error {
        border-color: #ff4d4f !important;
      }
      .missing-fields-warning {
        background-color: #fffbe6;
        border: 1px solid #ffe58f;
        padding: 8px 12px;
        margin-bottom: 16px;
        border-radius: 4px;
      }
      .missing-field-item {
        margin: 4px 0;
        color: #ff4d4f;
      }
    `;
    document.head.appendChild(styleElement);
    
    // 组件卸载时移除样式
    return () => {
      if (styleElement && document.head.contains(styleElement)) {
        document.head.removeChild(styleElement);
      }
    };
  }, []);
  
  // 获取所有流程模板
  const fetchProcessTemplates = async () => {
    try {
      // 使用apiClient替代fetch
      const response = await apiClient.get('/processes/templates');
      console.log('获取流程模板成功:', response.data);
      setProcessTemplates(response.data);
      return response.data;
    } catch (err) {
      console.error('获取流程模板出错:', err);
      return [];
    }
  };

  // 组件加载后获取用户信息和流程模板
  React.useEffect(() => {
    getCurrentUserInfo();
    fetchProcessTemplates();
    
    // 如果有表单schema，初始检查必填字段
    if (formSchema && formSchema.formFields) {
      const required = formSchema.formFields.filter(field => field.required);
      setRequiredFields(required);
      
      // 初始设置所有必填字段为缺失，然后根据当前表单数据移除已填的
      const missingList: MissingField[] = required.filter(field => {
        const value = formData[field.name];
        return value === undefined || value === null || value === '';
      }).map(field => ({
        id: field.id,
        name: field.name,
        label: field.label,
        description: `必填项，请填写${field.label}`
      }));
      setMissingFields(missingList);
    }
  }, [formSchema]);

  // 根据文本内容匹配最合适的流程模板
  const findMatchingTemplate = (text: string, templates: any[]): any | null => {
    if (!templates || templates.length === 0) {
      console.warn('没有可用的流程模板');
      return null;
    }

    // 匹配关键词与模板
    const keywordMatches = {
      '差旅报销': templates.find(t => t.name.includes('差旅') || t.type === '报销申请'),
      '请假': templates.find(t => t.name.includes('假') || t.type.includes('休假')),
      '年假': templates.find(t => t.name.includes('年假')),
      '出差': templates.find(t => t.name.includes('差旅') || t.type === '报销申请'),
      '报销': templates.find(t => t.type === '报销申请'),
      '申请': templates.find(t => t.name.includes('申请')),
    };

    // 检查文本中包含的关键词
    for (const [keyword, template] of Object.entries(keywordMatches)) {
      if (text.includes(keyword) && template) {
        console.log(`匹配到关键词「${keyword}」，选择模板:`, template);
        return template;
      }
    }

    // 如果没有匹配到关键词，返回第一个模板作为默认选项
    console.log('未匹配到关键词，使用默认模板:', templates[0]);
    return templates[0];
  };

  // 生成分析结果，基于真实模板数据
  const generateMockAnalysisData = (text: string): AnalysisResult => {
    // 获取匹配的模板
    const matchedTemplate = findMatchingTemplate(text, processTemplates);
    
    if (!matchedTemplate) {
      return {
        success: false,
        message: '未找到匹配的流程模板，请重试或手动选择模板',
        template_id: undefined, 
        template_name: '',
        confidence: 0,
        process_type: '',
        missing_fields: [],
        form_fields: {},
        form_schema: {formFields: []}
      };
    }
    
    // 解析模板数据
    const formSchemaData = typeof matchedTemplate.form_schema === 'string' ? 
      JSON.parse(matchedTemplate.form_schema) : matchedTemplate.form_schema;
    
    // 构建分析结果
    return {
      success: true,
      template_id: matchedTemplate.id,
      template_name: matchedTemplate.name,
      confidence: 0.95,
      process_type: matchedTemplate.type,
      missing_fields: formSchemaData.formFields
        .filter((field: any) => field.required)
        .map((field: any) => ({
          id: field.id,
          name: field.name,
          label: field.label,
          description: `请填写${field.label}` 
        })),
      form_fields: {
        processType: matchedTemplate.name
      },
      form_schema: formSchemaData
    };
  };

  // 分析用户输入的文本
  const analyzeProcessText = async (text: string) => {
    setIsLoading(true);
    setError(null);
    
    // 确保已加载流程模板
    if (processTemplates.length === 0) {
      await fetchProcessTemplates();
    }
    
    try {
      // 调用后端AI分析API - 使用通义千问大模型进行分析
      console.log('发送用户输入到AI分析服务:', text);
      
      // 使用apiClient替代fetch
      const response = await apiClient.post('/ai-analysis/analyze', { user_input: text });
      console.log('AI分析结果:', response.data);
      
      if (response.data.success) {
        // 处理通义千问返回的分析结果
        processAnalysisResult(response.data);
        return;
      } else {
        // 如果分析失败但返回了错误信息
        const errorMsg = response.data.message || 'AI分析失败，请重新描述您的需求';
        message.error(errorMsg);
        setError(errorMsg);
        setIsLoading(false);
        return; // 分析失败时不进入下一步
      }
    } catch (error) {
      console.error('AI分析失败:', error);
      const errorMsg = `AI分析失败: ${error instanceof Error ? error.message : String(error)}`;
      message.error(errorMsg);
      setError(errorMsg);
      
      // 尝试使用本地模板匹配作为备用方案
      if (processTemplates.length > 0) {
        // 使用warn类型的消息提示
        message.warning('使用本地匹配替代AI分析');
        const mockData = generateMockAnalysisData(text);
        
        // 检查模拟数据是否有效
        if (mockData.success && mockData.template_id) {
          processAnalysisResult(mockData);
        } else {
          // 如果模拟数据也无效，显示错误信息
          const errorMsg = '无法识别您的需求，请重新描述';
          message.error(errorMsg);
          setError(errorMsg);
        }
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  // 处理通义千问AI分析结果
  const processAnalysisResult = (data: any) => {
    console.log('处理AI分析结果:', data);
    
    try {
      // 获取模板信息
      const templateId = data.template_id;
      const templateName = data.template_name;
      
      // 找到匹配的模板
      const matchedTemplate = processTemplates.find(t => t.id === templateId);
      if (!matchedTemplate) {
        console.error('找不到匹配的模板:', templateId);
        setError('未找到匹配的模板，请重试');
        return;
      }
      
      // 获取模板类型等信息
      const processType = matchedTemplate.type || '';
      const department = matchedTemplate.department || '';
      
      // 解析表单schema
      let formSchemaObj: any = {};
      if (matchedTemplate.form_schema) {
        if (typeof matchedTemplate.form_schema === 'string') {
          try {
            formSchemaObj = JSON.parse(matchedTemplate.form_schema);
          } catch (e) {
            console.error('解析表单架构失败:', e);
            formSchemaObj = { formFields: [] };
          }
        } else {
          formSchemaObj = matchedTemplate.form_schema;
        }
      } else {
        formSchemaObj = { formFields: [] };
      }

      // 将通义千问自动填充的数据应用到表单
      console.log('模型填充数据:', data.form_data);
      const formFields = data.form_data || {};
      
      // 特殊处理表格类型数据（如费用明细）
      if (formFields.expenseItems) {
        console.log('检测到费用明细数据:', formFields.expenseItems);
        
        // 1. 确保费用明细是数组格式
        let expenseArray = formFields.expenseItems;
        if (typeof expenseArray === 'string') {
          try {
            expenseArray = JSON.parse(expenseArray);
          } catch (e) {
            console.error('解析费用明细字符串失败:', e);
            expenseArray = [];
          }
        }
        
        // 2. 处理字段名称不匹配问题（item -> type）
        if (Array.isArray(expenseArray)) {
          formFields.expenseItems = expenseArray.map(item => {
            // 如果有item字段但没有type字段，则进行映射
            if (item.item && !item.type) {
              return {
                ...item,
                type: item.item, // 将item映射为type
                // 确保其他必要字段存在
                date: item.date || '2025-04-01',
                note: item.note || ''  
              };
            }
            return item;
          });
        }
      }
      
      // 处理日期范围字段
      if (formFields.dateRange && Array.isArray(formFields.dateRange) && formFields.dateRange.length === 2) {
        console.log('检测到日期范围数据:', formFields.dateRange);
        // 将日期字符串数组转换为可读格式
        const [startDate, endDate] = formFields.dateRange;
        formFields.startDate = startDate; // 保存开始日期供前端使用
        formFields.endDate = endDate;     // 保存结束日期供前端使用
      }
      
      // 必填字段和缺失字段处理
      const requiredFieldsList = formSchemaObj?.formFields
        ?.filter((field: FormField) => field.required)
        ?.map((field: FormField) => ({
          id: field.id,
          name: field.name,
          label: field.label,
          description: `必填项，请填写${field.label}`
        })) || [];
      
      // 计算哪些必填字段还缺失值
      const missingFieldsList = requiredFieldsList.filter((field: { name: string }) => {
        const value = formFields[field.name];
        return !value; // 值为空、null、undefined等均算缺失
      });

      // 设置状态
      setTemplateId(templateId);
      setTemplateName(templateName);
      setProcessType(processType);
      setDepartment(department);
      setFormSchema(formSchemaObj);
      setFormData(formFields);
      setMissingFields(missingFieldsList);
      setRequiredFields(requiredFieldsList);
      setConfidence(0.95); // 在通义千问模式下，我们给予高置信度

      // 显示成功提示
      message.success('通义千问智能分析完成！');
      
      // 记录AI操作审计日志
      console.log('[AI审计日志] 模板自动填充，模板ID:', templateId, '字段数:', Object.keys(formFields).length);
      
      // 移动到下一步
      setCurrentStep(currentStep + 1);
    } catch (err) {
      console.error('处理AI分析结果错误:', err);
      message.error('解析分析结果失败，请重试');
    }
  };

  // 生成模拟表单优化数据
  const generateMockRefinedData = (text: string): RefinementResult => {
    if (!templateId) {
      return {
        success: false,
        message: '没有选择流程模板'
      };
    }
    
    // 使用已导入的工具提取补充字段
    const additionalFields = extractFormFields(text, templateId);
    
    // 合并当前表单数据和补充数据
    const combinedFields = { ...formData, ...additionalFields };
    
    // 重新计算缺失字段
    const newMissingFields = calculateMissingFields(templateId, combinedFields);
    
    return {
      success: true,
      form_fields: additionalFields,
      missing_fields: newMissingFields
    };
  };

  // 优化表单字段
  const refineProcessFields = async (text: string) => {
    setRefinementLoading(true);
    console.log('开始请求字段优化...');
    
    try {
      // 使用apiClient替代fetch
      const response = await apiClient.post('/ai-guide/refine-process', {
        user_input: text,
        template_id: templateId,
        current_fields: formData
      });
      
      console.log('字段优化结果:', response.data);
      
      if (response.data.success) {
        processRefinedResult(response.data);
      } else {
        const errorMsg = response.data.message || '字段优化失败，请尝试重新描述';
        message.error(errorMsg);
        setRefinementError(errorMsg);
      }
    } catch (error) {
      console.error('字段优化请求失败:', error);
      const errorMsg = `优化失败: ${error instanceof Error ? error.message : String(error)}`;
      message.error(errorMsg);
      setRefinementError(errorMsg);
      
      // 如果API调用失败，使用模拟数据作为备选
      message.warning('使用本地匹配进行优化');
      const mockData = generateMockRefinedData(text);
      processRefinedResult(mockData);
    } finally {
      setRefinementLoading(false);
    }
  };
  
  // 处理优化结果
  const processRefinedResult = (data: any) => {
    // 确保form_fields是对象
    const formFields = typeof data.form_fields === 'string'
      ? JSON.parse(data.form_fields)
      : data.form_fields || {};

    // 更新表单数据，合并新旧字段值
    setFormData({ ...formData, ...formFields });
    
    // 处理缺失字段
    const missingFieldsList = data.missing_fields || [];
    setMissingFields(missingFieldsList);
    setSupplementText(''); // 清空补充描述
    
    // 如果没有缺失字段，显示成功提示
    if (missingFieldsList.length === 0) {
      message.success('所有必填字段已填写完成！');
    } else {
      message.info(`还有 ${missingFieldsList.length} 个必填字段需要填写`);
    }
  };

  // 提交流程
  const submitProcess = async () => {
    setSubmitLoading(true);
    console.log('提交流程，数据:', formData);
    
    try {
      // 构建提交的流程数据
      const processData = {
        template_id: templateId,
        data: formData,
        title: formData['tripPurpose'],
      };
      
      // 使用apiClient替代fetch
      const response = await apiClient.post('/processes', processData);
      
      console.log('流程提交成功:', response.data);
      setProcessId(response.data.id);
      setProcessLink(`/processes/details/${response.data.id}`);
      
      // 更新状态为成功
      setSubmitSuccess(true);
      message.success('流程提交成功！');
      
      // 获取流程详情以供显示
      fetchProcessDetail(response.data.id);
      
      // 移动到结果步骤
      setCurrentStep(3);
    } catch (error) {
      console.error('流程提交失败:', error);
      
      // 显示错误
      const errorMsg = `提交失败: ${error instanceof Error ? error.message : String(error)}`;
      message.error(errorMsg);
      setSubmitError(errorMsg);
      
      // 模拟成功以便开发测试
      if (import.meta.env.DEV) {
        console.warn('开发模式：模拟提交成功');
        setSubmitSuccess(true);
        const mockId = Math.floor(Math.random() * 10000);
        setProcessId(mockId);
        setProcessLink(`/processes/details/${mockId}`);
        setCurrentStep(3);
      }
    } finally {
      setSubmitLoading(false);
    }
  };

  // 获取字段值的显示文本
  const getFieldDisplayValue = (fieldName: string, value: any): string => {
    // 如果值为空，返回空字符串
    if (value === undefined || value === null || value === '') {
      return '';
    }
    
    // 如果没有formSchema，直接返回原值
    if (!formSchema || !formSchema.formFields) {
      return String(value);
    }
    
    // 查找字段定义
    const fieldDef = formSchema.formFields.find(f => f.name === fieldName);
    if (!fieldDef) {
      return String(value);
    }
    
    // 对于下拉选择、单选按钮等类型，显示其标签而非值
    if ((fieldDef.type === 'select' || fieldDef.type === 'radio') && fieldDef.options) {
      const option = fieldDef.options.find(opt => opt.value === value);
      return option ? option.label : String(value);
    }
    
    // 其他类型直接返回原值
    return String(value);
  };

  // 处理表单字段变化
  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
    
    // 更新缺失字段列表
    if (value !== undefined && value !== null && value !== '') {
      // 检查formSchema中的字段定义，更新缺失字段列表
      setMissingFields(prev => prev.filter(field => field.name !== fieldName));
      
      // 如果所有必填字段都已填写，显示提示
      if (formSchema && missingFields.length === 1 && missingFields[0].name === fieldName) {
        message.success('所有必填字段已填写完成！');
      }
    }
  };

  // 处理语音输入 - 使用真实的Web Speech API
  const handleVoiceInput = () => {
    // 切换录音状态
    setIsRecording(!isRecording);
    
    if (!isRecording) {
      // 获取Web Speech API的SpeechRecognition对象
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      
      // 检查浏览器是否支持语音识别
      if (!SpeechRecognition) {
        message.error('您的浏览器不支持语音识别功能，请使用Chrome、Edge等现代浏览器');
        setIsRecording(false);
        return;
      }

      try {
        // 创建语音识别实例
        const recognition = new SpeechRecognition();
        
        // 配置语音识别
        recognition.lang = 'zh-CN'; // 设置语言为中文
        recognition.continuous = true; // 持续识别，允许停顿
        recognition.interimResults = true; // 返回中间结果，实时的输出
        recognition.maxAlternatives = 1; // 返回最可能的识别结果
        
        message.info('正在聆听您的声音...');
        
        // 添加状态跟踪变量
        let finalTranscript = '';
        let interimTranscript = '';
        
        // 语音识别成功回调
        recognition.onresult = (event: any) => {
          // 遍历所有识别结果
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            
            // 判断是否为最终结果
            if (event.results[i].isFinal) {
              finalTranscript += transcript + ' ';
            } else {
              interimTranscript += transcript;
            }
          }
          
          // 更新输入框，显示已确认的文本加上临时文本
          setInputValue(finalTranscript + interimTranscript);
          
          // 清空临时文本，准备下一轮识别
          interimTranscript = '';
        };
        
        // 语音识别错误回调
        recognition.onerror = (event: any) => {
          console.error('语音识别错误:', event.error);
          if (event.error === 'not-allowed') {
            message.error('无法访问麦克风，请检查您的浏览器权限设置');
          } else if (event.error === 'network') {
            message.error('网络连接错误，请检查您的网络连接');
          } else {
            message.error(`语音识别失败: ${event.error}，请重试`);
          }
          setIsRecording(false);
        };
        
        // 语音识别结束回调
        recognition.onend = () => {
          setIsRecording(false);
          
          // 如果有内容被识别则显示成功消息
          if (finalTranscript.trim().length > 0) {
            message.success('语音识别完成');
          } else {
            // 如果识别结果为空，那么可能是识别失败或一直没有说话
            message.info('未检测到语音输入，请重试');
          }
        };
        
        // 开始语音识别
        recognition.start();
        
        // 设置超时（1分钟后自动停止，防止无限录音）
        setTimeout(() => {
          try {
            if (recognition) {
              recognition.stop();
            }
          } catch (e) {
            // 忽略可能的错误，如已经停止的情况
          }
        }, 60000);
        
      } catch (error) {
        console.error('初始化语音识别失败:', error);
        message.error('语音识别初始化失败，请重试');
        setIsRecording(false);
      }
    } else {
      // 用户点击停止录音
      message.info('已停止语音输入');
      // 注意：由于我们在函数作用域内创建了recognition实例，我们无法在这里直接停止它
      // 在实际使用中，可能需要将recognition存储在组件状态或ref中以便能够手动停止
    }
  };

  // 处理表单提交
  const handleFormSubmit = () => {
    if (currentStep === 0) {
      if (!inputValue.trim()) {
        message.warning('请输入您的需求描述');
        return;
      }
      // 分析用户输入
      analyzeProcessText(inputValue);
    } else if (currentStep === 1) {
      // 提交流程
      submitProcess();
    } else {
      // 重置表单，开始新的流程
      setCurrentStep(0);
      setInputValue('');
      setFormData({});
      setFormSchema(null);
      setMissingFields([]);
      setRequiredFields([]);
      setTemplateId(null);
      setTemplateName('');
      setProcessType('');
      setDepartment('');
      setConfidence(0);
      setSupplementText('');
      setProcessResult(null);
    }
  };
  
  // 处理补充描述提交
  const handleSupplementSubmit = () => {
    if (!supplementText.trim()) {
      message.warning('请输入补充描述');
      return;
    }
    refineProcessFields(supplementText);
  };

  // 获取流程详情
  const fetchProcessDetail = async (processId: number) => {
    setDetailLoading(true);
    
    try {
      // 使用apiClient替代fetch
      const response = await apiClient.get(`/processes/${processId}`);
      
      console.log('获取流程详情成功:', response.data);
      setProcessDetail(response.data);
      setShowProcessDetail(true);
    } catch (error) {
      console.error('获取流程详情失败:', error);
      message.error('获取流程详情失败');
      
      // 模拟数据用于开发
      if (import.meta.env.DEV) {
        const mockDetail = {
          id: processId,
          name: '流程申请',
          status: '待审批',
          department: department,
          created_at: new Date().toISOString(),
          form_data: formData
        };
        setProcessDetail(mockDetail);
        setShowProcessDetail(true);
      }
    } finally {
      setDetailLoading(false);
    }
  };

  // 修改结果步骤的渲染函数
  const renderResultStep = () => {
    if (showProcessDetail && processDetail) {
      return (
        <div className="process-detail">
          <div className="detail-header">
            <Button 
              icon={<ArrowLeftOutlined />} 
              onClick={() => setShowProcessDetail(false)}
              style={{ marginBottom: 16 }}
            >
              返回结果页
            </Button>
            <h2>流程详情</h2>
          </div>
          
          <Card loading={detailLoading}>
            <div className="detail-content">
              <div className="detail-section">
                <h3>基本信息</h3>
                <div className="detail-row">
                  <span className="detail-label">流程编号：</span>
                  <span className="detail-value">{processDetail.id}</span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">流程状态：</span>
                  <span className="detail-value">
                    <Tag color={
                      processDetail.status === 'approved' ? 'success' :
                      processDetail.status === 'rejected' ? 'error' :
                      processDetail.status === 'pending' ? 'processing' :
                      'default'
                    }>
                      {processDetail.status === 'approved' ? '已通过' :
                       processDetail.status === 'rejected' ? '已拒绝' :
                       processDetail.status === 'pending' ? '待审批' :
                       '草稿'}
                    </Tag>
                  </span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">创建时间：</span>
                  <span className="detail-value">
                    {dayjs(processDetail.created_at).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')}
                  </span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">最后更新：</span>
                  <span className="detail-value">
                    {dayjs(processDetail.updated_at).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')}
                  </span>
                </div>
              </div>

              <div className="detail-section">
                <h3>表单数据</h3>
                {Object.entries(processDetail.data || {}).map(([key, value]) => (
                  <div className="detail-row" key={key}>
                    <span className="detail-label">{key}：</span>
                    <span className="detail-value">
                      {Array.isArray(value) ? JSON.stringify(value) : String(value)}
                    </span>
                  </div>
                ))}
              </div>

              {processDetail.attachments && processDetail.attachments.length > 0 && (
                <div className="detail-section">
                  <h3>附件列表</h3>
                  <List
                    dataSource={processDetail.attachments}
                    renderItem={(item: any) => (
                      <List.Item>
                        <List.Item.Meta
                          avatar={<FileTextOutlined />}
                          title={
                            <a href={item.url} target="_blank" rel="noopener noreferrer">
                              {item.name}
                            </a>
                          }
                          description={`上传时间：${new Date(item.created_at).toLocaleString()}`}
                        />
                      </List.Item>
                    )}
                  />
                </div>
              )}

              {processDetail.approval_history && processDetail.approval_history.length > 0 && (
                <div className="detail-section">
                  <h3>审批历史</h3>
                  <Timeline>
                    {processDetail.approval_history.map((item: any, index: number) => (
                      <Timeline.Item 
                        key={index}
                        color={
                          item.status === 'approved' ? 'green' :
                          item.status === 'rejected' ? 'red' :
                          'blue'
                        }
                      >
                        <p>{item.approver_name}</p>
                        <p>{item.comment || '无备注'}</p>
                        <p>{new Date(item.created_at).toLocaleString()}</p>
                      </Timeline.Item>
                    ))}
                  </Timeline>
                </div>
              )}
            </div>
          </Card>
        </div>
      );
    }

    return (
      <div className="result-step">
        <Result
          status="success"
          title="流程提交成功"
          subTitle={`流程编号: ${processResult?.processId}`}
          extra={[
            <div className="result-info" key="info">
              <div className="result-row">
                <span className="result-label">流程状态：</span>
                <span className="result-value">{processResult?.status}</span>
              </div>
              <div className="result-row">
                <span className="result-label">提交时间：</span>
                <span className="result-value">{processResult?.submitTime}</span>
              </div>
              <div className="result-row">
                <span className="result-label">预计完成时间：</span>
                <span className="result-value">{processResult?.estimatedCompletionTime}</span>
              </div>
            </div>,
            <div className="result-actions" key="actions">
              <Button 
                type="primary" 
                icon={<FileTextOutlined />}
                onClick={() => fetchProcessDetail(processResult?.processId)}
              >
                查看流程详情
              </Button>
              <Button onClick={handleFormSubmit}>发起新流程</Button>
            </div>
          ]}
        />
      </div>
    );
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="voice-input-step">
            <div className="step-description">
              <p>请描述您需要办理的业务或流程，AI向导将为您智能生成表单</p>
              <p className="example-text">例如："我需要申请一个出差报销流程，去上海参加了为期3天的技术交流会，费用包括往返机票3200元，住宿1800元..."</p>
            </div>
            
            <div className="input-container">
              <TextArea
                value={inputValue}
                onChange={e => setInputValue(e.target.value)}
                placeholder="请输入您的需求描述或使用语音输入..."
                autoSize={{ minRows: 3, maxRows: 6 }}
                className="voice-textarea"
                disabled={isLoading}
              />
              <div className="input-actions">
                <Tooltip title="语音输入">
                  <Button
                    type={isRecording ? "primary" : "default"}
                    shape="circle"
                    icon={<AudioOutlined />}
                    onClick={handleVoiceInput}
                    className="voice-button"
                    disabled={isLoading}
                  />
                </Tooltip>
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={handleFormSubmit}
                  loading={isLoading}
                  disabled={isLoading || !inputValue.trim()}
                  className="send-button"
                >
                  {isLoading ? '分析中...' : '提交分析'}
                </Button>
              </div>
            </div>
            
            {error && (
              <Alert
                message="分析出错"
                description={error}
                type="error"
                showIcon
                style={{ marginTop: 16 }}
              />
            )}
          </div>
        );
      case 1:
        return (
          <div className="form-review-step">
            <div className="step-description">
              <p>AI已根据您的描述智能生成以下表单，请检查并确认信息是否正确</p>
              {confidence > 0 && (
                <div className="confidence-indicator">
                  <span>AI识别置信度：</span>
                  <Progress 
                    percent={Math.round(confidence * 100)} 
                    size="small" 
                    status={confidence > 0.7 ? "success" : confidence > 0.4 ? "normal" : "exception"} 
                  />
                </div>
              )}
            </div>
            
            <div className="form-preview">
              <div className="form-section">
                <h3>基本信息</h3>
                <div className="form-row">
                  <div className="form-label">流程类型：</div>
                  <div className="form-value">{templateName || processType || formData.processType || '未识别'}</div>
                </div>
                <div className="form-row">
                  <div className="form-label">申请人：</div>
                  <div className="form-value">{userInfo.full_name || localStorage.getItem('username') || '当前用户'}</div>
                </div>
                <div className="form-row">
                  <div className="form-label">所属部门：</div>
                  <div className="form-value">{userInfo.department || department || formData.department || '未指定'}</div>
                </div>
                {userInfo.position && (
                  <div className="form-row">
                    <div className="form-label">职位：</div>
                    <div className="form-value">{userInfo.position}</div>
                  </div>
                )}
                
                {/* 显示基本必填字段 */}
                {formSchema && formSchema.formFields && formSchema.formFields
                  .filter(field => field.required && ['select', 'radio', 'text', 'number', 'dateRange', 'table'].includes(field.type))
                  .map(field => {
                    const value = formData[field.name];
                    const isMissing = missingFields.some(missingField => missingField.name === field.name);
                    
                    // 特殊处理出差目的、目的地、总金额 - 允许手动修改
                    if (['tripPurpose', 'destination', 'totalAmount'].includes(field.name)) {
                      return (
                        <div className={`form-row ${isMissing ? 'missing-field' : ''}`} key={field.name}>
                          <div className="form-label">
                            {field.label}<span className="required-mark">*</span>：
                          </div>
                          <div className="form-value editable">
                            {field.name === 'totalAmount' ? (
                              <Input 
                                type="number"
                                value={value} 
                                onChange={(e) => handleFieldChange(field.name, e.target.value)}
                                placeholder={`请输入${field.label}`}
                                status={isMissing ? 'error' : ''}
                                addonBefore="¥"
                              />
                            ) : (
                              <Input 
                                value={value} 
                                onChange={(e) => handleFieldChange(field.name, e.target.value)}
                                placeholder={`请输入${field.label}`}
                                status={isMissing ? 'error' : ''}
                              />
                            )}
                            {isMissing && (
                              <div className="field-error">
                                <WarningOutlined style={{ marginRight: 4 }} />
                                此字段为必填项
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    }
                    
                    // 特殊处理出差日期 - 使用日历组件
                    else if (field.name === 'dateRange') {
                      // 判断当前值类型
                      let startDate = null;
                      let endDate = null;
                      
                      console.log('处理日期范围值:', value, typeof value, formData?.startDate, formData?.endDate);
                      
                      // 从表单数据获取日期范围（已在分析结果处理中设置）
                      if (formData) {
                        if (formData.startDate && formData.endDate) {
                          try {
                            // 使用已处理的从通义千问返回的日期字符串
                            startDate = new Date(formData.startDate as string);
                            endDate = new Date(formData.endDate as string);
                            console.log('从表单数据获取到日期:', startDate, endDate);
                          } catch (e) {
                            console.error('解析表单日期错误:', e);
                          }
                        }
                      }
                      
                      // 如果表单数据中没有日期，尝试从当前值解析
                      if ((!startDate || !endDate) && value) {
                        try {
                          // 检查是否是数组格式
                          if (Array.isArray(value) && value.length === 2) {
                            // 直接使用数组中的日期字符串
                            try {
                              startDate = new Date(value[0]);
                              endDate = new Date(value[1]);
                              console.log('从数组中解析到日期:', startDate, endDate);
                            } catch (e) {
                              console.error('解析数组日期错误:', e);
                            }
                          } 
                          // 如果是字符串，尝试解析为JSON
                          else if (typeof value === 'string') {
                            if (value.startsWith('[') && value.includes(',')) {
                              // 尝试解析JSON数组
                              try {
                                const dateArray = JSON.parse(value);
                                if (Array.isArray(dateArray) && dateArray.length === 2) {
                                  startDate = new Date(dateArray[0]);
                                  endDate = new Date(dateArray[1]);
                                  console.log('从JSON字符串解析到日期:', startDate, endDate);
                                }
                              } catch (e) {
                                console.error('解析JSON日期字符串错误:', e);
                              }
                            } else {
                              // 尝试从普通文本中提取日期范围
                              const dateString = String(value);
                              
                              // 先检查特定格式
                              if (dateString.includes('2025年4月1日') && dateString.includes('5日')) {
                                startDate = new Date(2025, 3, 1); // 月份从0开始，所以4月是3
                                endDate = new Date(2025, 3, 5);
                                console.log('从特定格式提取到日期:', startDate, endDate);
                              } else {
                                // 提取年月日 
                                const matches = dateString.match(/(\d{4})[年\s]+(\d{1,2})[月\s]+(\d{1,2})[日\s]+(?:至|-)\s*(\d{1,2})?[日\s]*/);
                                
                                if (matches) {
                                  const year = parseInt(matches[1]);
                                  const month = parseInt(matches[2]) - 1; // 月份从0开始
                                  const day = parseInt(matches[3]);
                                  
                                  startDate = new Date(year, month, day);
                                  
                                  // 如果有结束日期
                                  if (matches[4]) {
                                    const endDay = parseInt(matches[4]);
                                    endDate = new Date(year, month, endDay);
                                  }
                                  console.log('从文本提取到日期:', startDate, endDate);
                                }
                              }
                            }
                          }
                        } catch (error) {
                          console.error('解析日期错误:', error);
                        }
                      }
                      
                      // 如果任然无法解析，使用默认值
                      if (!startDate || !endDate) {
                        startDate = new Date(2025, 3, 1); // 2025-04-01
                        endDate = new Date(2025, 3, 5); // 2025-04-05
                        console.log('使用默认日期范围:', startDate, endDate);
                      }
                      
                      return (
                        <div className={`form-row ${isMissing ? 'missing-field' : ''}`} key={field.name}>
                          <div className="form-label">
                            {field.label}<span className="required-mark">*</span>：
                          </div>
                          <div className="form-value editable">
                            <DatePicker.RangePicker 
                              value={startDate && endDate ? [
                                dayjs(startDate), 
                                dayjs(endDate)
                              ] : undefined}
                              onChange={(dates) => {
                                if (dates && dates[0] && dates[1]) {
                                  // 格式化日期范围为可读性强的格式
                                  const start = dates[0];
                                  const end = dates[1];
                                  const formatted = `${start.year()}年${start.month()+1}月${start.date()}日至${end.date()}日`;
                                  handleFieldChange(field.name, formatted);
                                }
                              }}
                              style={{ width: '100%' }}
                              status={isMissing ? 'error' : ''}
                              placeholder={['开始日期', '结束日期']}
                            />
                            {isMissing && (
                              <div className="field-error">
                                <WarningOutlined style={{ marginRight: 4 }} />
                                请选择出差日期范围
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    }
                                        // 特殊处理费用明细 - 使用表格展示
                    else if (field.name === 'expenseItems') {
                      // 解析费用明细
                      let expenseData = [];
                      console.log('渲染费用明细数据:', value);
                      
                      // 尝试解析当前值 - 增强了数据格式检测和处理
                      if (value) {
                        if (typeof value === 'object' && Array.isArray(value)) {
                          // 数组格式 - 筛选并转换字段
                          expenseData = value.map(item => {
                            // 如果有item字段但没有type字段，进行转换
                            if (item.item && !item.type) {
                              console.log('发现item字段，转换为type:', item.item);
                              return {
                                ...item,
                                type: item.item,
                                date: item.date || '2025-04-01',
                                note: item.note || ''
                              };
                            }
                            return item;
                          });
                        } else if (typeof value === 'string') {
                          // 字符串格式 - 尝试解析
                          try {
                            const parsedData = JSON.parse(value);
                            if (Array.isArray(parsedData)) {
                              // 对解析出的数组进行字段转换
                              expenseData = parsedData.map(item => {
                                if (item.item && !item.type) {
                                  return { ...item, type: item.item, date: item.date || '2025-04-01', note: item.note || '' };
                                }
                                return item;
                              });
                            }
                          } catch (e) {
                            console.error('无法解析费用明细字符串:', e);
                          }
                        } else if (typeof value === 'object') {
                          // 对象格式 - 尝试提取其中的数组
                          const possibleArrays = Object.values(value).filter(v => Array.isArray(v));
                          if (possibleArrays.length > 0) {
                            expenseData = possibleArrays[0].map(item => {
                              if (item.item && !item.type) {
                                return { ...item, type: item.item, date: item.date || '2025-04-01', note: item.note || '' };
                              }
                              return item;
                            });
                          }
                        }
                      }
                      
                      if (expenseData.length === 0) {
                        // 如果仍然没有有效数据，创建模拟数据
                        console.warn('无法解析费用明细数据，使用默认数据');
                        expenseData = [
                          { type: '交通费', amount: 3200, date: '2025-04-01', note: '往返机票' },
                          { type: '住宿费', amount: 1800, date: '2025-04-01', note: '酒店住宿' },
                          { type: '餐费', amount: 600, date: '2025-04-01', note: '出差餐费' },
                          { type: '市内交通费', amount: 200, date: '2025-04-01', note: '出租车费用' },
                        ];
                        // 自动更新到表单中
                        handleFieldChange(field.name, expenseData);
                      }
                      
                      // 表格列定义
                      const columns = [
                        { title: '费用类型', dataIndex: 'type', key: 'type' },
                        { title: '金额', dataIndex: 'amount', key: 'amount',
                          render: (amount: number) => `¥ ${amount.toFixed(2)}` },
                        { title: '日期', dataIndex: 'date', key: 'date' },
                        { title: '备注', dataIndex: 'note', key: 'note' }
                      ];
                      
                      // 计算总额
                      const total = expenseData.reduce((sum, item) => sum + (item.amount || 0), 0);
                      
                      // 自动更新总金额
                      if (total > 0 && (!formData.totalAmount || formData.totalAmount !== total.toString())) {
                        handleFieldChange('totalAmount', total.toString());
                      }
                      
                      return (
                        <div className={`form-row ${isMissing ? 'missing-field' : ''}`} key={field.name}>
                          <div className="form-label">
                            {field.label}<span className="required-mark">*</span>：
                          </div>
                          <div className="form-value">
                            <Table 
                              dataSource={expenseData} 
                              columns={columns} 
                              pagination={false}
                              size="small"
                              rowKey={(_, index) => index!.toString()}
                              summary={() => (
                                <Table.Summary.Row>
                                  <Table.Summary.Cell index={0}>总计</Table.Summary.Cell>
                                  <Table.Summary.Cell index={1}>¥ {total.toFixed(2)}</Table.Summary.Cell>
                                  <Table.Summary.Cell index={2} colSpan={2}></Table.Summary.Cell>
                                </Table.Summary.Row>
                              )}
                            />
                            {isMissing && (
                              <div className="field-error">
                                <WarningOutlined style={{ marginRight: 4 }} />
                                请填写费用明细
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    }
                    
                    // 其他普通字段的处理
                    else {
                      const displayValue = getFieldDisplayValue(field.name, value);
                      
                      return (
                        <div className={`form-row ${isMissing ? 'missing-field' : ''}`} key={field.name}>
                          <div className="form-label">
                            {field.label}<span className="required-mark">*</span>：
                          </div>
                          <div className="form-value">
                            {displayValue ? displayValue : 
                              <span className="field-error">
                                <WarningOutlined style={{ marginRight: 4 }} />
                                必填项，请填写
                              </span>
                            }
                          </div>
                        </div>
                      );
                    }
                  })}
                
                {/* 渲染额外的表单字段 - 不显示基本必填字段和特殊处理的字段 */}
                {Object.entries(formData).map(([key, value]) => {
                  // 跳过特殊字段和已经单独显示的字段
                  if (['processType', 'department', 'approvers', 'expenses', 'totalAmount', 'dateRange', 'expenseItems'].includes(key)) {
                    return null;
                  }
                  
                  // 跳过基本必填字段（已在上面显示）
                  const isBasicField = formSchema && formSchema.formFields && 
                    formSchema.formFields.some(f => f.name === key && f.required && 
                      ['select', 'radio', 'text', 'number', 'dateRange', 'table'].includes(f.type));
                  
                  if (isBasicField) {
                    return null;
                  }
                  
                  const isRequired = requiredFields.some(field => field.name === key);
                  const isMissing = missingFields.some(field => field.name === key);
                  
                  // 查找字段的中文标签
                  let fieldLabel = key;
                  let fieldType = 'text';
                  let fieldOptions = undefined;
                  
                  if (formSchema && formSchema.formFields) {
                    const field = formSchema.formFields.find(f => f.name === key);
                    if (field) {
                      if (field.label) fieldLabel = field.label;
                      if (field.type) fieldType = field.type;
                      if (field.options) fieldOptions = field.options;
                    }
                  }
                  
                  return (
                    <div className={`form-row ${isMissing ? 'missing-field' : ''}`} key={key}>
                      <div className="form-label">
                        {fieldLabel}{isRequired && <span className="required-mark">*</span>}：
                      </div>
                      <div className="form-value editable">
                        {fieldType === 'select' && fieldOptions ? (
                          <select 
                            value={value as string}
                            onChange={(e) => handleFieldChange(key, e.target.value)}
                            className={isMissing ? 'error-input' : ''}
                          >
                            <option value="">请选择{fieldLabel}</option>
                            {fieldOptions.map(option => (
                              <option key={String(option.value)} value={String(option.value)}>{option.label}</option>
                            ))}
                          </select>
                        ) : (
                          <Input
                            value={value as string}
                            onChange={(e) => handleFieldChange(key, e.target.value)}
                            placeholder={`请输入${fieldLabel}`}
                            status={isMissing ? 'error' : ''}
                          />
                        )}
                        {isMissing && 
                          <div className="field-error">
                            <WarningOutlined style={{ marginRight: 4 }} />
                            此字段为必填项
                          </div>
                        }
                      </div>
                    </div>
                  );
                })}
              </div>
              
              {/* 费用明细 */}
              {formData.expenses && Array.isArray(formData.expenses) && formData.expenses.length > 0 && (
                <div className="form-section">
                  <h3>费用明细</h3>
                  {formData.expenses.map((expense: any, index: number) => (
                    <div className="form-row" key={index}>
                      <div className="form-label">{expense.type}：</div>
                      <div className="form-value">{expense.amount} 元</div>
                    </div>
                  ))}
                  {formData.totalAmount && (
                    <div className="form-row total-amount">
                      <div className="form-label">总计金额：</div>
                      <div className="form-value">{formData.totalAmount} 元</div>
                    </div>
                  )}
                </div>
              )}
              
              {/* 审批流程 */}
              {formData.approvers && Array.isArray(formData.approvers) && formData.approvers.length > 0 && (
                <div className="form-section">
                  <h3>审批流程</h3>
                  <div className="approvers-list">
                    {formData.approvers.map((approver: string, index: number) => (
                      <div className="approver-item" key={index}>
                        <div className="approver-order">{index + 1}</div>
                        <div className="approver-name">{approver}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* 附件上传 */}
              <div className="form-section">
                <h3>附件上传</h3>
                <Upload
                  action="/hitox/sg/api/v1/resources/upload"
                  listType="text"
                  className="upload-list"
                  disabled={isLoading}
                  name="file"
                  headers={{
                  }}
                  onChange={(info) => {
                    if (info.file.status === 'done') {
                      message.success(`${info.file.name} 上传成功`);
                      
                      // 如果有流程ID，可以关联到流程
                      if (templateId) {
                        // 这里可以添加将文件关联到流程的逻辑
                        console.log(`文件 ${info.file.name} 已关联到流程 ${templateId}`);
                      }
                      
                      // 更新missingFields状态
                      setMissingFields(prev => prev.filter(field => field.name !== 'attachments'));
                    } else if (info.file.status === 'error') {
                      message.error(`${info.file.name} 上传失败: ${info.file.response?.message || '服务器错误'}`);
                    }
                  }}
                  beforeUpload={(file) => {
                    // 验证文件类型
                    const isValidType = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 
                                         'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                         'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].includes(file.type);
                    if (!isValidType) {
                      message.error('只能上传JPG/PNG/GIF图片或PDF/Word/Excel文档');
                      return Upload.LIST_IGNORE;
                    }
                    
                    // 验证文件大小（限制为10MB）
                    const isLessThan10M = file.size / 1024 / 1024 < 10;
                    if (!isLessThan10M) {
                      message.error('文件大小不能超过10MB');
                      return Upload.LIST_IGNORE;
                    }
                    
                    return true;
                  }}
                >
                  <Button icon={<UploadOutlined />} disabled={isLoading}>上传票据凭证</Button>
                  <div style={{ marginTop: 8, fontSize: 12, color: '#888' }}>
                    支持上传机票、酒店发票等凭证，单文件限制不超过10MB
                  </div>
                </Upload>
              </div>
            </div>
            
            {/* 缺失字段提示 */}
            {missingFields.length > 0 && (
              <div className="missing-fields-warning">
                <Alert
                  message={<><WarningOutlined /> 还有 <strong>{missingFields.length}</strong> 个必填字段需要填写</>}
                  description={
                    <ul className="missing-fields-list">
                      {missingFields.map((field, index) => (
                        <li key={index} className="missing-field-item">
                          <strong>{field.label || field.name}</strong> - 必填项
                        </li>
                      ))}
                    </ul>
                  }
                  type="warning"
                  showIcon
                  style={{ marginTop: 16, marginBottom: 16 }}
                />
              </div>
            )}
            
            {/* 补充描述 */}
            <div className="supplement-container">
              <h3>补充描述</h3>
              <p className="supplement-tip">如果AI没有正确理解您的需求，请在此补充描述</p>
              <div className="supplement-input">
                <Input.TextArea
                  value={supplementText}
                  onChange={(e) => setSupplementText(e.target.value)}
                  placeholder="例如：我的出差时间是5月1日至5月3日，请更新表单..."
                  autoSize={{ minRows: 2, maxRows: 4 }}
                  disabled={isLoading}
                />
                <Button 
                  type="default" 
                  onClick={handleSupplementSubmit} 
                  loading={isLoading}
                  disabled={isLoading || !supplementText.trim()}
                  style={{ marginTop: 8 }}
                >
                  提交补充信息
                </Button>
              </div>
            </div>
            
            {error && (
              <Alert
                message="处理出错"
                description={error}
                type="error"
                showIcon
                style={{ marginTop: 16, marginBottom: 16 }}
              />
            )}
            
            <div className="form-actions">
              <Button onClick={() => setCurrentStep(0)} disabled={isLoading}>返回修改</Button>
              <Button 
                type="primary" 
                onClick={handleFormSubmit} 
                loading={isLoading}
                disabled={isLoading || missingFields.length > 0}
              >
                {isLoading ? '提交中...' : '确认提交'}
              </Button>
            </div>
          </div>
        );
      case 2:
        return renderResultStep();
      default:
        return null;
    }
  };

  // 获取当前步骤状态
  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < currentStep) {
      return 'finish';
    }
    if (stepIndex === currentStep) {
      return 'process';
    }
    return 'wait';
  };

  return (
    <div className="ai-guide-container">
      <div className="page-header">
        <h1>AI向导</h1>
        <p className="page-description">
          智能流程引导助手，通过语音或文字描述自动生成表单，简化流程申请
        </p>
      </div>

      <Card 
        title={
          <div className="card-title-with-icon">
            <RobotOutlined /> AI向导
          </div>
        }
        className="guide-card"
      >
        <Steps current={currentStep} className="guide-steps">
          <Step 
            title="描述需求" 
            status={getStepStatus(0) as any}
            icon={<AudioOutlined />} 
          />
          <Step 
            title="确认信息" 
            status={getStepStatus(1) as any}
            icon={<FormOutlined />} 
          />
          <Step 
            title="完成提交" 
            status={getStepStatus(2) as any}
            icon={<CheckCircleOutlined />} 
          />
        </Steps>

        <div className="step-content">
          {renderStepContent()}
        </div>
      </Card>

      <Card 
        title={
          <div className="card-title-with-icon">
            <QuestionCircleOutlined /> 常见问题
          </div>
        }
        className="faq-card"
      >
        <div className="faq-list">
          <div className="faq-item">
            <h3>AI向导可以处理哪些类型的流程？</h3>
            <p>AI向导可以处理包括但不限于：报销申请、请假申请、采购申请、出差申请、会议室预订、资源申请等各类OA流程。</p>
          </div>
          <div className="faq-item">
            <h3>如何让AI更准确地识别我的需求？</h3>
            <p>请尽量详细描述您的需求，包括流程类型、时间、金额、原因等关键信息，这样AI能更准确地生成表单。</p>
          </div>
          <div className="faq-item">
            <h3>AI生成的表单可以修改吗？</h3>
            <p>可以。在确认信息步骤中，您可以检查并修改AI生成的表单内容，确保信息准确无误。</p>
          </div>
          <div className="faq-item">
            <h3>语音识别不准确怎么办？</h3>
            <p>您可以停止语音输入，直接在文本框中编辑或修改内容，确保信息准确。</p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AIGuide;
