/**
 * 全局配置文件
 */

// 根据当前URL自动判断环境
const getEnvironment = (): 'development' | 'test' | 'production' => {
  const hostname = window.location.hostname;

  if (hostname.includes('local') || hostname.includes('127.0.0.1')) {
    return 'development';
  } else if (hostname.includes('test') || hostname.includes('staging')) {
    return 'test';
  } else {
    return 'production';
  }
};

const env = getEnvironment();
console.log('Detected environment based on URL:', env);

// 基础配置
const config = {
  // API基础路径
  apiBasePath: '/hitox/sg/api/v1',

  // 聊天机器人配置
  chatbot: {
    // 聊天机器人URL模板，${token}会被替换为实际的token
    // 开发环境
    development: {
      baseUrl: 'http://*************:31115/chatbot',
      urlTemplate: 'http://*************:31115/chatbot/${token}'
    },
    // 测试环境
    test: {
      baseUrl: '/hitoflow/web/chatbot',
      urlTemplate: '/hitoflow/web/chatbot/${token}'
    },
    // 生产环境
    production: {
      baseUrl: '/hitoflow/web/chatbot',
      urlTemplate: '/hitoflow/web/chatbot/${token}'
    }
  }
};

// 根据当前环境获取配置
export const getChatbotConfig = () => {
  return config.chatbot[env as keyof typeof config.chatbot] || config.chatbot.development;
};

export default config;
