// 声明环境变量类型
declare global {
  interface Window {
    _env_: {
      REACT_APP_API_BASE_URL: string;
    };
  }
}

// API基础配置
export const API_CONFIG = {
  // API基础地址 - 使用环境变量中的相对路径
  BASE_URL: window._env_?.REACT_APP_API_BASE_URL || '/hitox/sg',

  // API版本
  VERSION: 'v1',

  // API路径前缀
  PREFIX: 'api',

  // 获取完整的API基础URL
  getBaseUrl: () => `${API_CONFIG.BASE_URL}/${API_CONFIG.PREFIX}/${API_CONFIG.VERSION}`,

  // 流程相关API路径
  PROCESS: {
    TEMPLATES: '/processes/templates',
    PROCESSES: '/processes',
    APPROVALS: '/processes/approvals',
    MY_PROCESSES: '/processes/my-processes'
  },

  // 用户相关API路径
  USER: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    PROFILE: '/users/profile'
  }
};

// 导出完整的API URL
export const API_URLS = {
  // 流程模板相关
  PROCESS_TEMPLATES: `${API_CONFIG.getBaseUrl()}${API_CONFIG.PROCESS.TEMPLATES}`,
  PROCESSES: `${API_CONFIG.getBaseUrl()}${API_CONFIG.PROCESS.PROCESSES}`,
  PROCESS_APPROVALS: `${API_CONFIG.getBaseUrl()}${API_CONFIG.PROCESS.APPROVALS}`,
  MY_PROCESSES: `${API_CONFIG.getBaseUrl()}${API_CONFIG.PROCESS.MY_PROCESSES}`,

  // 用户相关
  USER_LOGIN: `${API_CONFIG.getBaseUrl()}${API_CONFIG.USER.LOGIN}`,
  USER_LOGOUT: `${API_CONFIG.getBaseUrl()}${API_CONFIG.USER.LOGOUT}`,
  USER_PROFILE: `${API_CONFIG.getBaseUrl()}${API_CONFIG.USER.PROFILE}`
};