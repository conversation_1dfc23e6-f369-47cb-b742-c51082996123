import React, { lazy, Suspense, useEffect, useState } from 'react';
import { Navigate, useRoutes } from 'react-router-dom';
import MainLayout from '../layouts/MainLayout';
import axios from 'axios';

// 懒加载组件
const Dashboard = lazy(() => import('../pages/dashboard/Dashboard'));
const AISentinel = lazy(() => import('../pages/ai-assistant/AISentinel'));
const AIGuide = lazy(() => import('../pages/ai-assistant/AIGuide'));
const MyProcess = lazy(() => import('../pages/process/MyProcess'));
const ProcessTemplates = lazy(() => import('../pages/process/ProcessTemplates'));
const ProcessDesigner = lazy(() => import('../pages/process/ProcessDesigner'));
const EnterpriseKnowledge = lazy(() => import('../pages/knowledge/EnterpriseKnowledge'));
const DocumentCenter = lazy(() => import('../pages/knowledge/DocumentCenter'));
const CrossModalSearch = lazy(() => import('../pages/knowledge/CrossModalSearch'));
const NewKnowledgeBase = lazy(() => import('../pages/knowledge/NewKnowledgeBase'));
const FileManager = lazy(() => import('../pages/file/FileManager'));
const OAChat = lazy(() => import('../pages/communication/OAChat'));
const ReportGeneration = lazy(() => import('../pages/report/ReportGeneration'));
const ReportTemplates = lazy(() => import('../pages/report/ReportTemplates'));
const DifyAppsManagement = lazy(() => import('../pages/Settings/DifyApps'));

// 加载中组件
const LoadingComponent = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    padding: '20px'
  }}>
    正在加载...
  </div>
);

// 路由守卫组件 - 已修改，暂时移除本地 Token 检查
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  // const token = localStorage.getItem('token');
  // const tokenType = localStorage.getItem('token_type');

  // // 检查是否有token和类型
  // const isAuthenticated = token && tokenType;

  // if (!isAuthenticated) {
  //   // 清除可能存在的无效数据
  //   localStorage.removeItem('token');
  //   localStorage.removeItem('token_type');
  //   localStorage.removeItem('user');
  //   // return <Navigate to=\"/login\" replace />;
  //   console.warn("ProtectedRoute: No local token found, proceeding without redirect (Header auth assumed).");
  // }

  // TODO: Implement proper check based on API call / Redux state
  // For now, always render children assuming header-based auth will work
  return <>{children}</>;
};

const AppRoutes = () => {
  const [powerMenus, setPowerMenus] = useState([]);

  useEffect(() => {
    axios.get('/hitox/access-manager/common/menuList?menuUse=oa').then(res => {
      if (res.status == 200 && res.data.success) {
        setPowerMenus(res.data.data);
      }
    })
  }, []);

  const defaultSelect = () => {
    for (const iter of powerMenus) {
      if (iter.routes) {
        return iter.routes[0].path
      } else {
        return iter.path;
      }
    }
  }

  const routes = useRoutes([
    {
      path: '/',
      element: <Navigate to={defaultSelect()} replace />
    },
    {
      path: '/',
      element: (
        < ProtectedRoute >
          <MainLayout powerMenus={powerMenus} />
        </ProtectedRoute >
      ),
      children: [
        {
          path: 'dashboard',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <Dashboard />
            </Suspense>
          )
        },
        {
          path: 'ai-assistant/sentinel',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <AISentinel />
            </Suspense>
          )
        },
        {
          path: 'ai-assistant/guide',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <AIGuide />
            </Suspense>
          )
        },
        // 其他路由将在后续实现
        {
          path: 'process/my',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <MyProcess />
            </Suspense>
          )
        },
        {
          path: 'process/templates',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <ProcessTemplates />
            </Suspense>
          )
        },
        {
          path: 'process/designer',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <ProcessDesigner />
            </Suspense>
          )
        },
        {
          path: 'knowledge/enterprise',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <EnterpriseKnowledge />
            </Suspense>
          )
        },
        {
          path: 'knowledge/documents',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <DocumentCenter />
            </Suspense>
          )
        },
        {
          path: 'knowledge/search',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <CrossModalSearch />
            </Suspense>
          )
        },
        {
          path: 'knowledge/new-base',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <NewKnowledgeBase />
            </Suspense>
          )
        },
        {
          path: 'communication/chat',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <OAChat />
            </Suspense>
          )
        },
        {
          path: 'communication/wechat',
          element: <div>微信联通（待实现）</div>
        },
        {
          path: 'communication/meeting',
          element: <div>在线会议（待实现）</div>
        },
        {
          path: 'file-management/processing',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <FileManager />
            </Suspense>
          )
        },
        {
          path: 'file-management/seal',
          element: <div>电子印章（待实现）</div>
        },
        {
          path: 'report/generation',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <ReportGeneration />
            </Suspense>
          )
        },
        {
          path: 'report/templates',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <ReportTemplates />
            </Suspense>
          )
        },
        {
          path: 'settings/personal',
          element: <div>个人设置（待实现）</div>
        },
        {
          path: 'settings/department',
          element: <div>部门管理（待实现）</div>
        },
        {
          path: 'settings/system',
          element: <div>系统配置（待实现）</div>
        },
        {
          path: 'settings/dify-apps',
          element: (
            <Suspense fallback={<LoadingComponent />}>
              <DifyAppsManagement />
            </Suspense>
          )
        }
      ]
    },
    {
      path: '*',
      element: <div>404 页面未找到</div>
    }
  ]);

  return routes;
};

export default AppRoutes;
