#!/usr/bin/env python3
"""
测试用户绑定关系修复
"""
import os
import sys

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.db.session import engine, get_db
from app.models.user import User
from app.models.user_dify_app_binding import UserDifyAppBinding

def test_user_model():
    """测试用户模型"""
    print("测试用户模型...")
    
    # 创建一个用户实例
    user = User(
        username="test_user",
        email="<EMAIL>",
        external_user_id="test_123"
    )
    
    print(f"✅ 用户模型创建成功: {user.username}")
    
    # 测试访问不存在的关系属性（应该会失败）
    try:
        binding = user.dify_app_binding
        print(f"❌ 意外成功访问了 dify_app_binding 关系: {binding}")
    except AttributeError as e:
        print(f"✅ 正确抛出 AttributeError: {e}")
    
    return user

def test_binding_model():
    """测试绑定模型"""
    print("\n测试绑定模型...")
    
    # 创建一个绑定实例
    binding = UserDifyAppBinding(
        user_id=1,
        dify_app_id="test_app_123",
        dify_site_access_token="test_token_456"
    )
    
    print(f"✅ 绑定模型创建成功: user_id={binding.user_id}, app_id={binding.dify_app_id}")
    
    return binding

def test_database_query():
    """测试数据库查询"""
    print("\n测试数据库查询...")
    
    try:
        # 获取数据库会话
        db = next(get_db())
        
        # 测试查询用户
        user_count = db.query(User).count()
        print(f"✅ 用户表查询成功，当前用户数: {user_count}")
        
        # 测试查询绑定
        binding_count = db.query(UserDifyAppBinding).count()
        print(f"✅ 绑定表查询成功，当前绑定数: {binding_count}")
        
        # 测试关联查询（手动方式）
        if user_count > 0:
            user = db.query(User).first()
            binding = db.query(UserDifyAppBinding).filter(
                UserDifyAppBinding.user_id == user.id
            ).first()
            
            if binding:
                print(f"✅ 找到用户 {user.id} 的绑定: {binding.dify_app_id}")
            else:
                print(f"ℹ️ 用户 {user.id} 暂无绑定")
        
        db.close()
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        import traceback
        traceback.print_exc()

def test_api_dependencies():
    """测试 API 依赖项"""
    print("\n测试 API 依赖项...")
    
    try:
        from app.api.dependencies import get_current_user
        print("✅ get_current_user 依赖项导入成功")
        
        from app.api.v1.endpoints.dify_management import get_my_dify_app_info
        print("✅ dify_management 端点导入成功")
        
    except Exception as e:
        print(f"❌ API 依赖项测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=" * 60)
    print("测试用户绑定关系修复")
    print("=" * 60)
    
    # 测试模型
    user = test_user_model()
    binding = test_binding_model()
    
    # 测试数据库查询
    test_database_query()
    
    # 测试 API 依赖项
    test_api_dependencies()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("\n修复总结:")
    print("1. ✅ 移除了 User 模型中的 dify_app_binding 关系定义")
    print("2. ✅ 更新了 dependencies.py 中的代码，使用直接查询")
    print("3. ✅ 更新了 dify_management.py 中的代码，使用直接查询")
    print("4. ✅ 所有模块可以正常导入，没有语法错误")
    print("\n现在应用程序可以正常运行，不会再出现 AttributeError!")
