#!/usr/bin/env python3
"""
测试自定义 schema 配置
"""
import os
import sys

# 设置自定义 schema 环境变量
os.environ["DB_SCHEMA"] = "oa_test"

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.db.base import Base
from app.db.session import engine

# 导入一些关键模型
from app.models.user import User
from app.models.process import Process, ProcessTemplate

def test_custom_schema():
    """测试自定义 schema 配置"""
    print(f"当前配置的 POSTGRES_SCHEMA: {settings.POSTGRES_SCHEMA}")
    print(f"数据库连接 URL: {settings.DATABASE_URL}")
    print()
    
    print("检查关键表的 schema 配置:")
    print("-" * 50)
    
    # 检查关键表的 schema 配置
    key_tables = ['users', 'process_templates', 'processes']
    for table_name in key_tables:
        full_table_name = f"{settings.POSTGRES_SCHEMA}.{table_name}"
        if full_table_name in Base.metadata.tables:
            table = Base.metadata.tables[full_table_name]
            print(f"表名: {table_name:<20} 完整名称: {full_table_name:<30} Schema: {table.schema}")
        else:
            print(f"表名: {table_name:<20} 未找到")

def create_custom_schema():
    """创建自定义 schema"""
    try:
        from sqlalchemy import text
        with engine.connect() as conn:
            # 检查 schema 是否存在
            result = conn.execute(text(f"SELECT schema_name FROM information_schema.schemata WHERE schema_name = '{settings.POSTGRES_SCHEMA}'"))
            schema_exists = result.fetchone()
            
            if not schema_exists:
                print(f"创建自定义 schema: {settings.POSTGRES_SCHEMA}")
                conn.execute(text(f"CREATE SCHEMA IF NOT EXISTS {settings.POSTGRES_SCHEMA}"))
                conn.commit()
                print(f"Schema '{settings.POSTGRES_SCHEMA}' 创建成功")
            else:
                print(f"Schema '{settings.POSTGRES_SCHEMA}' 已存在")
                
    except Exception as e:
        print(f"创建 schema 失败: {e}")

def demonstrate_schema_usage():
    """演示如何在不同 schema 中使用表"""
    print("\n演示 schema 使用:")
    print("-" * 50)
    
    # 显示如何在查询中使用 schema
    print("在 SQLAlchemy 查询中，表会自动使用配置的 schema:")
    print(f"User 表的完整名称: {User.__table__.fullname}")
    print(f"ProcessTemplate 表的完整名称: {ProcessTemplate.__table__.fullname}")
    print(f"Process 表的完整名称: {Process.__table__.fullname}")
    
    print("\n外键关系也会自动解析到正确的 schema:")
    for fk in Process.__table__.foreign_keys:
        print(f"Process 表外键: {fk.parent.name} -> {fk.target_fullname}")

if __name__ == "__main__":
    print("=" * 60)
    print("测试自定义 PostgreSQL Schema 配置")
    print("=" * 60)
    
    # 测试自定义 schema 配置
    test_custom_schema()
    
    print()
    print("=" * 60)
    
    # 创建自定义 schema
    try:
        create_custom_schema()
    except Exception as e:
        print(f"无法连接数据库: {e}")
        print("这是正常的，因为我们使用了测试 schema")
    
    print()
    print("=" * 60)
    
    # 演示 schema 使用
    demonstrate_schema_usage()
    
    print()
    print("=" * 60)
    print("测试完成!")
    print()
    print("使用说明:")
    print("1. 通过设置环境变量 DB_SCHEMA 来指定不同的 schema")
    print("2. 例如: export DB_SCHEMA=oa_production")
    print("3. 或者在 .env 文件中设置: DB_SCHEMA=oa_development")
    print("4. 所有表和外键关系都会自动使用指定的 schema")
