from pydantic import BaseSettings, AnyHttpUrl, PostgresDsn
import os
from typing import Optional, Dict, Any, List
import pytz


class Settings(BaseSettings):
    PROJECT_NAME: str = "智能OA系统 API"
    API_V1_STR: str = "/api/v1"

    DIFY_DOMAIN: str = os.getenv("DIFY_DOMAIN", "http://*************:31117")

    # 数据库设置
    POSTGRES_USER: str = os.getenv("DB_USERNAME", "postgres")
    POSTGRES_PASSWORD: str = os.getenv("DB_PASSWORD", "snbb123456")
    POSTGRES_DB: str = os.getenv("DB_DATABASE", "oa_system")
    POSTGRES_SCHEMA: str = os.getenv("DB_SCHEMA", "public")
    POSTGRES_ADDRESS: str = os.getenv("DB_ADDRESS", "localhost:5432")

    @property
    def POSTGRES_SERVER(self) -> str:
        """从POSTGRES_ADDRESS中获取服务器地址，处理可能包含协议的情况"""
        address = self.POSTGRES_ADDRESS

        # 移除协议部分（如果存在）
        if "://" in address:
            address = address.split("://")[1]

        # 分离主机和端口
        if ":" in address:
            return address.split(":")[0]
        return address

    @property
    def POSTGRES_PORT(self) -> str:
        """从POSTGRES_ADDRESS中获取端口号，处理可能包含协议的情况"""
        address = self.POSTGRES_ADDRESS

        # 移除协议部分（如果存在）
        if "://" in address:
            address = address.split("://")[1]

        # 分离主机和端口
        if ":" in address:
            return address.split(":")[1]
        return "5432"  # 默认端口

    @property
    def DATABASE_URL(self) -> str:
        # 优先使用环境变量中的数据库URL
        if os.getenv("DATABASE_URL"):
            return os.getenv("DATABASE_URL")

        # 否则使用PostgreSQL配置
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_ADDRESS}/{self.POSTGRES_DB}"

    # 保留SQLite路径属性，用于数据迁移
    @property
    def DATABASE_PATH(self) -> str:
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
        return os.path.join(base_dir, "data", "db", "sg", "oa_system.db")

    # JWT设置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days

    # Dify Console 登录凭证 (用于自动化操作)
    DIFY_CONSOLE_EMAIL: str = os.getenv("DIFY_CONSOLE_EMAIL", "<EMAIL>")
    DIFY_CONSOLE_PASSWORD: str = os.getenv("DIFY_CONSOLE_PASSWORD","snbb123456")

    # Dify API设置
    DIFY_API_BASE_URL: str = DIFY_DOMAIN # 注意：登录接口通常不在 /v1 下，而是在根路径 /console/api
    DIFY_API_KEY: str = os.getenv("DIFY_API_KEY", "dify-api-key-xxxxxxxxxxxx")  # 替换为实际的 API Key

    # Dify Datasets API设置
    DIFY_DATASETS_API_KEY: str = os.getenv("DIFY_DATASETS_API_KEY", "dataset-yrzUmA4wtK9a0paYMkZMpo9G")  # 从环境变量获取Dify datasets的访问密钥

    # 应用ID配置
    DIFY_CHAT_APP_ID: str = "chat-app-id"  # 聊天应用ID
    DIFY_SPEECH_TO_TEXT_APP_ID: str = "speech-to-text-app-id"  # 语音转文字应用ID

    # 阿里云通义千问设置
    DASHSCOPE_API_KEY: str = os.getenv("DASHSCOPE_API_KEY", "")  # 通过环境变量设置，避免硬编码

    API_ROOT_PATH: str = os.getenv("API_ROOT_PATH", "/hitox/sg")

    # 文件上传设置
    @property
    def UPLOAD_DIR(self) -> str:
        """文件上传目录"""
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        upload_dir = os.path.join(base_dir, "uploads")
        os.makedirs(upload_dir, exist_ok=True)  # 确保目录存在
        return upload_dir

    # 最大上传文件大小，默认 10MB
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024

    # 开发模式设置
    MOCK_GATEWAY_HEADERS: bool = True # 是否模拟网关注入 Header (用于本地开发)
    MOCK_DIFY_API: bool = True  # 开发环境下模拟Dify API响应

    # 工作流配置 - 预设的多个工作流应用
    DIFY_WORKFLOWS: Dict[str, Dict[str, Any]] = {
        "process_analysis": {
            "id": "process-analysis-workflow-id",  # 流程分析工作流ID
            "description": "分析文本内容，确定适合的流程类型和提取表单字段",
            "api_key": "process-analysis-api-key-xxxxxxxxxxxx",  # 替换为实际的 API Key
        },
        "process_refinement": {
            "id": "process-refinement-workflow-id",  # 流程字段优化工作流ID
            "description": "根据用户补充描述优化表单字段值",
            "api_key": "process-refinement-api-key-xxxxxxxxxxxx",  # 替换为实际的 API Key
        },
        "document_extraction": {
            "id": "document-extraction-workflow-id",  # 文档信息提取工作流ID
            "description": "从文档中提取关键信息和结构化数据",
            "api_key": "document-extraction-api-key-xxxxxxxxxxxx",  # 替换为实际的 API Key
        },
        "report_generation": {
            "id": "report-generation-workflow-id",  # 报告生成工作流ID
            "description": "根据数据和模板生成专业报告",
            "api_key": "report-generation-api-key-xxxxxxxxxxxx",  # 替换为实际的 API Key
        },
        "task_assignment": {
            "id": "task-assignment-workflow-id",  # 任务分配工作流ID
            "description": "分析任务内容并推荐合适的处理人员",
            "api_key": "task-assignment-api-key-xxxxxxxxxxxx",  # 替换为实际的 API Key
        }
    }

    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = []  # 改为普通的字符串列表

    # 时区配置
    TIMEZONE: str = "Asia/Shanghai"

    @property
    def TZ(self):
        return pytz.timezone(self.TIMEZONE)

    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
# Add print statements for debugging the setting values during import
print(f"[DEBUG config.py] MOCK_GATEWAY_HEADERS = {settings.MOCK_GATEWAY_HEADERS}")
print(f"[DEBUG config.py] POSTGRES_ADDRESS = {settings.POSTGRES_ADDRESS}")
print(f"[DEBUG config.py] POSTGRES_SERVER = {settings.POSTGRES_SERVER}")
print(f"[DEBUG config.py] POSTGRES_PORT = {settings.POSTGRES_PORT}")
