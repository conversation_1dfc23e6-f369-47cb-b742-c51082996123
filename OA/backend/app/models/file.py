from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base

class File(Base):
    """文件模型（去掉外键约束，支持分库分表）

    用于存储上传文件的元数据
    """
    __tablename__ = 'files'

    id = Column(Integer, primary_key=True, index=True)
    original_name = Column(String, nullable=False, comment='原始文件名')
    stored_name = Column(String, nullable=False, comment='存储文件名')
    path = Column(String, nullable=False, comment='文件路径')
    size = Column(Integer, nullable=False, default=0, comment='文件大小(字节)')
    file_type = Column(String, nullable=False, comment='文件类型')

    uploader_id = Column(Integer, nullable=True, comment='上传用户ID（逻辑外键）')
    # 关系暂时移除，避免复杂的连接配置问题
    # uploader = relationship('User', foreign_keys=[uploader_id], back_populates='files')

    process_id = Column(Integer, nullable=True, comment='关联流程ID（逻辑外键）')
    # process = relationship('Process', back_populates='files')

    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment='创建时间')
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment='更新时间')

    def __repr__(self):
        return f'<File {self.original_name}>'
