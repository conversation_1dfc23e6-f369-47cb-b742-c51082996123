from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class Contact(Base):
    """联系人模型"""
    __tablename__ = "contacts"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    avatar = Column(String)
    type = Column(String)  # user, group, ai
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class UserContact(Base):
    """用户联系人关系模型（去掉外键约束，支持分库分表）"""
    __tablename__ = "user_contacts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=True)  # 用户ID（逻辑外键）
    contact_id = Column(Integer, nullable=True)  # 联系人ID（逻辑外键）
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系暂时移除，避免复杂的连接配置问题
    # user = relationship("User", foreign_keys=[user_id])
    # contact = relationship("Contact", foreign_keys=[contact_id])


class Message(Base):
    """消息模型（去掉外键约束，支持分库分表）"""
    __tablename__ = "messages"

    id = Column(Integer, primary_key=True, index=True)
    contact_id = Column(Integer, nullable=True)  # 联系人ID（逻辑外键）
    sender_id = Column(Integer, nullable=True)  # 发送者ID（逻辑外键）
    content = Column(Text)
    content_type = Column(String, default="text")  # text, image, file
    file_info = Column(JSON, nullable=True)  # 如果是文件，存储文件信息
    status = Column(String)  # sending, sent, delivered, read, failed
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系暂时移除，避免复杂的连接配置问题
    # contact = relationship("Contact", foreign_keys=[contact_id])
    # sender = relationship("User", foreign_keys=[sender_id])


class AIConversation(Base):
    """AI对话模型，用于跟踪与Dify的对话（去掉外键约束，支持分库分表）"""
    __tablename__ = "ai_conversations"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=True)  # 用户ID（逻辑外键）
    dify_conversation_id = Column(String)  # Dify API返回的对话ID
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系暂时移除，避免复杂的连接配置问题
    # user = relationship("User", foreign_keys=[user_id])
