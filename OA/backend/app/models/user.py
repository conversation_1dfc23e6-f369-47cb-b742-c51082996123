from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.db.base import Base

from .user_dify_app_binding import UserDifyAppBinding


class User(Base):
    """用户模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    external_user_id = Column(String, unique=True, index=True, nullable=True)
    tenant_id = Column(String, index=True, nullable=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String, nullable=True)
    full_name = Column(String, nullable=True)
    department = Column(String, nullable=True)
    position = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关联关系暂时移除，避免复杂的连接配置问题
    # files = relationship("File", back_populates="uploader")
    # owned_knowledge_bases = relationship("KnowledgeBase", back_populates="owner")
    # dify_app_binding = relationship("UserDifyAppBinding", back_populates="user", uselist=False, cascade="all, delete-orphan")
