# app/models/knowledge.py
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, JSON, Table
from sqlalchemy.orm import relationship, foreign
from sqlalchemy.sql import func

from app.db.base_class import Base

# 知识库与标签的关联表（去掉外键约束，支持分库分表）
def _get_knowledge_base_tag_table():
    from app.core.config import settings
    return Table(
        'knowledge_base_tag',
        Base.metadata,
        Column('knowledge_base_id', Integer, primary_key=True, comment="知识库ID"),
        Column('tag_id', Integer, primary_key=True, comment="标签ID"),
        schema=settings.POSTGRES_SCHEMA
    )

knowledge_base_tag = _get_knowledge_base_tag_table()

class KnowledgeProvider(Base):
    """知识库供应商表"""
    __tablename__ = "knowledge_provider"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, comment="供应商名称，如Dify")
    provider_type = Column(String(20), nullable=False, comment="类型：api, local, custom等")
    base_url = Column(String(255), nullable=True, comment="API基础URL")
    api_key = Column(String(255), nullable=True, comment="API密钥")
    description = Column(Text, nullable=True, comment="描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系暂时移除，避免复杂的连接配置问题
    # knowledge_bases = relationship("KnowledgeBase", back_populates="provider")


class KnowledgeBase(Base):
    """知识库表（去掉外键约束，支持分库分表）"""
    __tablename__ = "knowledge_base"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="知识库名称")
    description = Column(Text, nullable=True, comment="知识库描述")
    icon = Column(String(255), nullable=True, comment="图标URL")
    provider_id = Column(Integer, nullable=True, comment="供应商ID（逻辑外键）")
    external_id = Column(String(100), nullable=True, comment="外部系统中的ID")
    config = Column(JSON, nullable=True, comment="特定供应商的配置信息")
    owner_id = Column(Integer, nullable=True, comment="所有者ID（逻辑外键）")
    is_public = Column(Boolean, default=False, comment="是否公开")
    is_active = Column(Boolean, default=True, comment="是否活跃")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系暂时移除，避免复杂的连接配置问题
    # provider = relationship("KnowledgeProvider", back_populates="knowledge_bases")
    # documents = relationship("KnowledgeDocument", back_populates="knowledge_base")
    # permissions = relationship("KnowledgePermission", back_populates="knowledge_base")
    # tags = relationship("KnowledgeTag", secondary=knowledge_base_tag, back_populates="knowledge_bases")
    # owner = relationship("User", back_populates="owned_knowledge_bases")


class KnowledgeDocument(Base):
    """知识库文档表（去掉外键约束，支持分库分表）"""
    __tablename__ = "knowledge_document"

    id = Column(Integer, primary_key=True, index=True)
    knowledge_base_id = Column(Integer, nullable=True, comment="知识库ID（逻辑外键）")
    name = Column(String(255), nullable=False, comment="文档名称")
    description = Column(Text, nullable=True, comment="文档描述")
    document_type = Column(String(50), nullable=True, comment="文档类型，如'text', 'file', 'web'等")
    external_id = Column(String(100), nullable=True, comment="外部系统中的文档ID")
    content_preview = Column(Text, nullable=True, comment="内容预览")
    doc_metadata = Column(JSON, nullable=True, comment="元数据，如文件大小、类型、来源等")
    status = Column(String(20), nullable=True, comment="状态：处理中、已完成、失败等")
    error_message = Column(Text, nullable=True, comment="错误信息（如有）")
    created_by = Column(Integer, nullable=True, comment="创建者ID（逻辑外键）")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系暂时移除，避免复杂的连接配置问题
    # knowledge_base = relationship("KnowledgeBase", back_populates="documents")
    # creator = relationship("User")


class KnowledgePermission(Base):
    """知识库权限表（去掉外键约束，支持分库分表）"""
    __tablename__ = "knowledge_permission"

    id = Column(Integer, primary_key=True, index=True)
    knowledge_base_id = Column(Integer, nullable=True, comment="知识库ID（逻辑外键）")
    user_id = Column(Integer, nullable=True, comment="用户ID（逻辑外键）")
    role_id = Column(Integer, nullable=True, comment="角色ID")
    permission_type = Column(String(20), nullable=False, comment="权限类型：read, write, admin等")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系暂时移除，避免复杂的连接配置问题
    # knowledge_base = relationship("KnowledgeBase", back_populates="permissions")
    # user = relationship("User")


class KnowledgeTag(Base):
    """知识库标签表"""
    __tablename__ = "knowledge_tag"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, comment="标签名称")
    color = Column(String(20), nullable=True, comment="颜色代码")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

    # 关系暂时移除，避免复杂的连接配置问题
    # knowledge_bases = relationship("KnowledgeBase", secondary=knowledge_base_tag, back_populates="tags")


class KnowledgeAuditLog(Base):
    """知识库操作日志表（去掉外键约束，支持分库分表）"""
    __tablename__ = "knowledge_audit_log"

    id = Column(Integer, primary_key=True, index=True)
    knowledge_base_id = Column(Integer, nullable=True, comment="知识库ID（逻辑外键）")
    document_id = Column(Integer, nullable=True, comment="文档ID（逻辑外键）")
    user_id = Column(Integer, nullable=True, comment="操作用户ID（逻辑外键）")
    action = Column(String(50), nullable=False, comment="操作类型: create, update, delete, query等")
    details = Column(JSON, nullable=True, comment="操作详情")
    ip_address = Column(String(50), nullable=True, comment="IP地址")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="操作时间")

    # 关系暂时移除，避免复杂的连接配置问题
    # knowledge_base = relationship("KnowledgeBase")
    # document = relationship("KnowledgeDocument")
    # user = relationship("User")


# 向后兼容的别名
Document = KnowledgeDocument
SearchLog = KnowledgeAuditLog