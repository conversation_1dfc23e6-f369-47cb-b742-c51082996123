from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum
from sqlalchemy import Enum as SQLAEnum

from app.db.base import Base


class ProcessStatus(str, Enum):
    """流程状态枚举"""
    DRAFT = "DRAFT"  # 草稿
    PENDING = "PENDING"  # 待审批
    APPROVED = "APPROVED"  # 已通过
    REJECTED = "REJECTED"  # 已拒绝
    CANCELLED = "CANCELLED"  # 已取消

    @classmethod
    def get_chinese_name(cls, status):
        """获取状态的中文名称"""
        status_map = {
            cls.DRAFT: "草稿",
            cls.PENDING: "待审批",
            cls.APPROVED: "已通过",
            cls.REJECTED: "已拒绝",
            cls.CANCELLED: "已取消"
        }
        return status_map.get(status, status)


class ProcessTemplate(Base):
    """流程模板模型（去掉外键约束，支持分库分表）"""
    __tablename__ = "process_templates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)  # 模板名称
    description = Column(Text)  # 模板描述
    type = Column(String, index=True)  # 模板类型（报销申请、休假申请等）
    department = Column(String, index=True)  # 所属部门
    steps = Column(JSON)  # 存储流程步骤的JSON数据
    form_schema = Column(JSON)  # 表单字段定义
    usage_count = Column(Integer, default=0)  # 使用次数
    version = Column(String)  # 模板版本
    status = Column(String, default="active")  # 状态（active, deprecated, draft）
    created_by = Column(Integer, nullable=True)  # 创建者ID（逻辑外键）
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系暂时移除，避免复杂的连接配置问题
    # creator = relationship("User", foreign_keys=[created_by])
    # processes = relationship("Process", back_populates="template")


class Process(Base):
    """流程实例模型（去掉外键约束，支持分库分表）"""
    __tablename__ = "processes"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, index=True)
    description = Column(Text)
    template_id = Column(Integer, nullable=True)  # 模板ID（逻辑外键）
    created_by = Column(Integer, nullable=True)  # 创建者ID（逻辑外键）
    current_step = Column(Integer)
    status = Column(SQLAEnum(ProcessStatus), default=ProcessStatus.PENDING)
    data = Column(JSON)  # 存储流程数据的JSON
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系暂时移除，避免复杂的连接配置问题
    # creator = relationship("User", foreign_keys=[created_by])
    # template = relationship("ProcessTemplate", back_populates="processes")
    # approvals = relationship("ProcessApproval", back_populates="process")
    # files = relationship("File", back_populates="process")


class ProcessApproval(Base):
    """流程审批记录模型（去掉外键约束，支持分库分表）"""
    __tablename__ = "process_approvals"

    id = Column(Integer, primary_key=True, index=True)
    process_id = Column(Integer, nullable=True)  # 流程ID（逻辑外键）
    step = Column(Integer)
    approver_id = Column(Integer, nullable=True)  # 审批人ID（逻辑外键）
    status = Column(String)  # pending, approved, rejected
    comment = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系暂时移除，避免复杂的连接配置问题
    # process = relationship("Process", back_populates="approvals")
    # approver = relationship("User", foreign_keys=[approver_id])
