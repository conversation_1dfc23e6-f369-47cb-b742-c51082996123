from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base


class ReportTemplate(Base):
    """报告模板模型（去掉外键约束，支持分库分表）"""
    __tablename__ = "report_templates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(Text)
    template_content = Column(Text)  # 可以是Markdown或HTML格式的模板
    parameters = Column(JSON)  # 模板参数定义
    created_by = Column(Integer, nullable=True)  # 创建者ID（逻辑外键）
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系暂时移除，避免复杂的连接配置问题
    # creator = relationship("User", foreign_keys=[created_by])
    # reports = relationship("Report", back_populates="template")


class Report(Base):
    """报告模型（去掉外键约束，支持分库分表）"""
    __tablename__ = "reports"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, index=True)
    description = Column(Text)
    template_id = Column(Integer, nullable=True)  # 模板ID（逻辑外键）
    created_by = Column(Integer, nullable=True)  # 创建者ID（逻辑外键）
    content = Column(Text)  # 生成的报告内容
    parameters = Column(JSON)  # 用于生成报告的参数
    ai_summary = Column(Text, nullable=True)  # AI生成的摘要
    ai_analysis = Column(Text, nullable=True)  # AI生成的分析
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系暂时移除，避免复杂的连接配置问题
    # creator = relationship("User", foreign_keys=[created_by])
    # template = relationship("ReportTemplate", back_populates="reports")
