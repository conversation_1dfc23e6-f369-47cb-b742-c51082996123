from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import relationship

from app.db.base_class import Base

class UserDifyAppBinding(Base):
    """用户Dify应用绑定模型（去掉外键约束，支持分库分表）"""
    __tablename__ = "user_dify_app_bindings"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, unique=True, nullable=True, index=True)  # 用户ID（逻辑外键）
    dify_app_id = Column(String, nullable=False, index=True)
    # Store the site access token, which might be long
    dify_site_access_token = Column(String(512), nullable=False)

    # 关系暂时移除，避免复杂的连接配置问题
    # user = relationship("User", back_populates="dify_app_binding")

# --- Add the back-reference to the User model ---
# You need to find your existing User model file (likely app/models/user.py)
# and add the corresponding relationship. Example below assumes your User model is
# in app/models/user.py and inherits from Base.

'''
# Example modification for app/models/user.py:
from sqlalchemy.orm import relationship
# ... other imports ...
from .user_dify_app_binding import UserDifyAppBinding # Import the new model

class User(Base):
    # ... existing columns ...

    # Add this relationship
    dify_app_binding = relationship("UserDifyAppBinding", back_populates="user", uselist=False, cascade="all, delete-orphan")

    # ... rest of the model ...
'''