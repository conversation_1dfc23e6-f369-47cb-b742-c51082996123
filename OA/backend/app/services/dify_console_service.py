import aiohttp
import logging
from typing import Optional, Dict, Any
import json # Import json for payload serialization
import asyncio # 导入 asyncio 用于锁
import requests # 导入 requests 库用于同步请求

from app.core.config import settings

logger = logging.getLogger(__name__)

# ---- Token 缓存 ----
_dify_console_tokens: Dict[str, Optional[str]] = {
    "access_token": None,
    "refresh_token": None
}
_token_refresh_lock = asyncio.Lock() # 添加锁防止并发刷新

async def _update_tokens(access_token: Optional[str], refresh_token: Optional[str]):
    """内部函数，用于更新缓存的 tokens"""
    global _dify_console_tokens
    _dify_console_tokens["access_token"] = access_token
    _dify_console_tokens["refresh_token"] = refresh_token
    if access_token:
        logger.info("Dify Console token updated.")
    else:
        logger.warning("Dify Console tokens cleared.")

async def login_dify_console() -> bool:
    """
    登录 Dify Console 并更新缓存的 access_token 和 refresh_token。

    Returns:
        bool: 登录是否成功。
    """
    login_url = f"{settings.DIFY_API_BASE_URL}/console/api/login" # 直接使用配置的基础URL
    payload = {
        "email": settings.DIFY_CONSOLE_EMAIL,
        "password": settings.DIFY_CONSOLE_PASSWORD,
    }
    headers = {
        "Content-Type": "application/json",
    }

    try:
        # 使用requests库替代aiohttp
        response = requests.post(login_url, json=payload, headers=headers, timeout=30.0)

        # 检查状态码
        if response.status_code >= 400:
            logger.error(f"Dify Console 登录失败 (状态码 {response.status_code})：{response.text}")
            await _update_tokens(None, None) # 清除旧 token
            return False

        try:
            response_data = response.json()
        except json.JSONDecodeError:
            logger.error(f"Dify Console 登录失败：无法解析 JSON 响应。响应: {response.text}")
            await _update_tokens(None, None)
            return False

        if response_data and response_data.get("data") and response_data["data"].get("access_token") and response_data["data"].get("refresh_token"):
            access_token = response_data["data"]["access_token"]
            refresh_token = response_data["data"]["refresh_token"]
            await _update_tokens(access_token, refresh_token)
            logger.info(f"成功登录 Dify Console (用户: {settings.DIFY_CONSOLE_EMAIL})")
            return True
        else:
            logger.error(f"Dify Console 登录失败：响应数据格式不正确或缺少 tokens。响应: {response.text}")
            await _update_tokens(None, None)
            return False

    except requests.RequestException as e:
        logger.error(f"Dify Console 登录请求网络错误：{e}")
        await _update_tokens(None, None)
        return False
    except Exception as e:
        logger.exception(f"Dify Console 登录时发生未知错误: {e}")
        await _update_tokens(None, None)
        return False

async def _refresh_dify_token_internal() -> bool:
    """内部函数：使用 refresh_token 刷新 access_token"""
    global _dify_console_tokens
    refresh_token = _dify_console_tokens.get("refresh_token")
    if not refresh_token:
        logger.warning("无法刷新 Dify token：缺少 refresh_token。")
        return False

    async with _token_refresh_lock: # 获取锁
        # 再次检查 token 是否在等待锁期间已被刷新
        if _dify_console_tokens.get("refresh_token") != refresh_token:
             logger.info("Dify token 在等待锁期间已被刷新，跳过本次刷新。")
             return True # 假设已被成功刷新

        logger.info("尝试刷新 Dify Console token...")
        refresh_url = f"{settings.DIFY_API_BASE_URL}/console/api/refresh-token"
        payload = {"refresh_token": refresh_token}
        headers = {"Content-Type": "application/json"}

        try:
            # 使用requests库替代aiohttp
            response = requests.post(refresh_url, json=payload, headers=headers, timeout=20.0)

            if response.status_code >= 400:
                logger.error(f"Dify Console token 刷新失败 (状态码 {response.status_code})：{response.text}")
                # 如果刷新失败（例如 refresh token 也过期了），清除所有 token，需要重新登录
                await _update_tokens(None, None)
                return False

            try:
                response_data = response.json()
            except json.JSONDecodeError:
                logger.error(f"Dify Console token 刷新失败：无法解析 JSON 响应。响应: {response.text}")
                return False # 保留旧的 refresh token，下次也许还能用

            if response_data and response_data.get("data") and response_data["data"].get("access_token") and response_data["data"].get("refresh_token"):
                new_access_token = response_data["data"]["access_token"]
                new_refresh_token = response_data["data"]["refresh_token"]
                await _update_tokens(new_access_token, new_refresh_token)
                logger.info("成功刷新 Dify Console token。")
                return True
            else:
                logger.error(f"Dify Console token 刷新失败：响应数据格式不正确或缺少 tokens。响应: {response.text}")
                return False
        except requests.RequestException as e:
            logger.error(f"Dify Console token 刷新请求网络错误：{e}")
            return False
        except Exception as e:
            logger.exception(f"Dify Console token 刷新时发生未知错误: {e}")
            return False

async def get_dify_console_access_token() -> Optional[str]:
    """
    获取当前缓存的 Dify Console Access Token。
    如果 token 不存在，会尝试重新登录一次。
    """
    global _dify_console_tokens
    token = _dify_console_tokens.get("access_token")
    if not token:
        logger.warning("Dify access token 不存在，尝试重新登录...")
        success = await login_dify_console() # 尝试登录以获取 token
        if success:
            token = _dify_console_tokens.get("access_token")
        else:
            logger.error("尝试重新登录 Dify Console 失败。")
            return None
    return token

# --- 新增：创建 Dify 应用的服务函数 ---
async def create_dify_app_service(name: str, description: Optional[str] = None, mode: str = "chat", icon: str = "🤖", icon_background: str = "#FFEAD5") -> Optional[Dict[str, Any]]:
    """
    调用 Dify Console API 创建一个新的应用。

    Args:
        name: 应用名称
        description: 应用描述 (可选)
        mode: 应用模式 (默认 'chat')
        icon: 图标 (可选)
        icon_background: 图标背景 (可选)

    Returns:
        Optional[Dict[str, Any]]: 成功则返回 Dify API 的响应数据 (包含新应用信息)，否则返回 None。
    """
    create_url = f"{settings.DIFY_API_BASE_URL}/console/api/apps"
    payload = {
        "name": name,
        "description": description,
        "mode": mode,
        "icon": icon,
        "icon_background": icon_background
    }

    def attempt_create(access_token: str) -> Optional[Dict[str, Any]]:
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        try:
            # 使用requests库替代aiohttp
            # 注意：Dify 创建接口可能需要更长的超时时间
            response = requests.post(create_url, json=payload, headers=headers, timeout=60.0)

            # 返回响应对象
            return {
                "status_code": response.status_code,
                "text": response.text,
                "response": response
            }
        except requests.RequestException as e:
            logger.error(f"调用 Dify 创建应用 API 网络错误：{e}")
            return None
        except Exception as e:
             logger.exception(f"调用 Dify 创建应用 API 时发生未知错误: {e}")
             return None

    access_token = await get_dify_console_access_token()
    if not access_token:
        logger.error("无法创建 Dify 应用：获取 Dify access token 失败。")
        return None

    response_data = attempt_create(access_token)

    if response_data is None: # 网络或其他错误
        return None

    # --- 处理 Token 过期 (401) ---
    if response_data["status_code"] == 401:
        logger.warning("Dify access token 可能已过期，尝试刷新 token 并重试...")
        refresh_successful = await _refresh_dify_token_internal()
        if refresh_successful:
            logger.info("Token 刷新成功，重试创建应用 API...")
            new_access_token = _dify_console_tokens.get("access_token")
            if new_access_token:
                 response_data = attempt_create(new_access_token)
                 if response_data is None: # 重试时网络错误
                      return None
            else:
                logger.error("刷新 token 后未能获取到新的 access token，无法重试。")
                return None
        else:
            logger.error("刷新 Dify token 失败，无法重试创建应用。")
            return None # 刷新失败，直接返回 None

    # --- 处理最终的响应 ---
    response = response_data["response"]
    if response_data["status_code"] == 201: # Dify 创建成功通常返回 201
        try:
            result_data = response.json()
            logger.info(f"成功调用 Dify API 创建应用: {name}")
            return result_data
        except json.JSONDecodeError:
            logger.error(f"创建 Dify 应用成功 (状态码 {response_data['status_code']})，但无法解析 JSON 响应: {response_data['text']}")
            return {"status": "created_but_no_json_response", "raw_response": response_data['text']} # 返回一个特殊标记
    else:
        logger.error(f"调用 Dify 创建应用 API 失败 (状态码 {response_data['status_code']})：{response_data['text']}")
        return None

# --- 新增：更新 Dify 应用模型配置的服务函数 ---
async def update_dify_app_model_config_service(app_id: str, config_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    调用 Dify Console API 更新指定应用的模型配置。

    Args:
        app_id: 要更新的应用的 ID。
        config_data: 包含新模型配置的字典。这个字典应该是 Dify /model-config 接口期望的完整格式。
                      例如，对于聊天或 Agent 应用，可能包含 'model', 'prompt_template', 'agent_mode', 'dataset_configs' 等。
                      其中 'dataset_configs' 可能包含 'retrieval_mode' 和 'datasets' (包含 'dataset_ids')。

    Returns:
        Optional[Dict[str, Any]]: 成功则返回 Dify API 的响应数据 (通常是一个简单的成功消息)，否则返回 None。
    """
    update_url = f"{settings.DIFY_API_BASE_URL}/console/api/apps/{app_id}/model-config"

    def attempt_update(access_token: str) -> Optional[Dict[str, Any]]:
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        try:
            # 使用requests库替代aiohttp
            # 更新配置接口的超时时间可以适当调整
            response = requests.post(update_url, json=config_data, headers=headers, timeout=45.0)

            # 返回响应对象
            return {
                "status_code": response.status_code,
                "text": response.text,
                "response": response
            }
        except requests.RequestException as e:
            logger.error(f"调用 Dify 更新模型配置 API ({app_id}) 网络错误：{e}")
            return None
        except Exception as e:
             logger.exception(f"调用 Dify 更新模型配置 API ({app_id}) 时发生未知错误: {e}")
             return None

    access_token = await get_dify_console_access_token()
    if not access_token:
        logger.error(f"无法更新 Dify 应用 ({app_id}) 配置：获取 Dify access token 失败。")
        return None

    response_data = attempt_update(access_token)

    if response_data is None: # 网络或其他错误
        return None

    # --- 处理 Token 过期 (401) ---
    if response_data["status_code"] == 401:
        logger.warning(f"Dify access token 可能已过期 (更新配置 {app_id})，尝试刷新 token 并重试...")
        refresh_successful = await _refresh_dify_token_internal()
        if refresh_successful:
            logger.info("Token 刷新成功，重试更新模型配置 API...")
            new_access_token = _dify_console_tokens.get("access_token")
            if new_access_token:
                 response_data = attempt_update(new_access_token)
                 if response_data is None: # 重试时网络错误
                      return None
            else:
                logger.error("刷新 token 后未能获取到新的 access token，无法重试。")
                return None
        else:
            logger.error("刷新 Dify token 失败，无法重试更新配置。")
            return None # 刷新失败，直接返回 None

    # --- 处理最终的响应 ---
    response = response_data["response"]
    if response_data["status_code"] == 200:
        try:
            result_data = response.json()
            logger.info(f"成功调用 Dify API 更新应用 ({app_id}) 的模型配置。")
            return result_data
        except json.JSONDecodeError:
            # 某些 Dify 版本可能在成功时不返回 JSON body，只返回 200 OK
            logger.info(f"成功调用 Dify API 更新应用 ({app_id}) 的模型配置 (状态码 200，无 JSON 响应)。")
            return {"status": "success"} # 返回通用成功消息
    else:
        logger.error(f"调用 Dify 更新模型配置 API ({app_id}) 失败 (状态码 {response_data['status_code']})：{response_data['text']}")
        return None

# --- 新增：获取 Dify 应用详情的服务函数 ---
async def get_dify_app_details_service(app_id: str) -> Optional[Dict[str, Any]]:
    """
    调用 Dify Console API 获取指定应用的详细信息，特别是 site.access_token。

    Args:
        app_id: 要获取详情的应用的 ID。

    Returns:
        Optional[Dict[str, Any]]: 成功则返回 Dify API 的响应数据 (包含应用详情)，否则返回 None。
    """
    details_url = f"{settings.DIFY_API_BASE_URL}/console/api/apps/{app_id}"
    max_retries = 3
    retry_count = 0

    def attempt_get_details(access_token: str) -> Optional[Dict[str, Any]]:
        headers = {
            "Authorization": f"Bearer {access_token}",
        }
        try:
            # 使用requests库替代aiohttp
            response = requests.get(details_url, headers=headers, timeout=30.0)

            # 返回响应对象
            return {
                "status_code": response.status_code,
                "text": response.text,
                "response": response
            }
        except requests.RequestException as e:
            logger.error(f"调用 Dify 获取应用详情 API ({app_id}) 网络错误：{e}")
            return None
        except Exception as e:
            logger.exception(f"调用 Dify 获取应用详情 API ({app_id}) 时发生未知错误: {e}")
            return None

    while retry_count < max_retries:
        try:
            access_token = await get_dify_console_access_token()
            if not access_token:
                logger.error(f"无法获取 Dify 应用 ({app_id}) 详情：获取 Dify access token 失败。")
                return None

            response_data = attempt_get_details(access_token)
            if response_data is None:
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"获取应用详情请求失败，尝试重试 (第 {retry_count}/{max_retries} 次)")
                    await asyncio.sleep(1)
                    continue
                return None

            if response_data["status_code"] == 401:
                logger.warning(f"Dify access token 可能已过期 (获取详情 {app_id})，尝试刷新 token 并重试...")
                refresh_successful = await _refresh_dify_token_internal()
                if refresh_successful:
                    logger.info("Token 刷新成功，重试获取应用详情 API...")
                    new_access_token = _dify_console_tokens.get("access_token")
                    if new_access_token:
                        response_data = attempt_get_details(new_access_token)
                        if response_data is None:
                            retry_count += 1
                            if retry_count < max_retries:
                                logger.info(f"Token刷新后获取失败，尝试重试 (第 {retry_count}/{max_retries} 次)")
                                await asyncio.sleep(1)
                                continue
                            return None
                    else:
                        logger.error("刷新 token 后未能获取到新的 access token，无法重试。")
                        return None
                else:
                    logger.error("刷新 Dify token 失败，无法重试获取详情。")
                    return None

            response = response_data["response"]
            if response_data["status_code"] == 200:
                try:
                    result_data = response.json()
                    if result_data.get('site') and result_data['site'].get('access_token'):
                        logger.info(f"成功调用 Dify API 获取应用 ({app_id}) 的详情。")
                        return result_data
                    else:
                        logger.error(f"获取 Dify 应用 ({app_id}) 详情成功，但响应中缺少 site.access_token。响应：{result_data}")
                        return None
                except Exception as e:
                    logger.exception(f"处理 Dify 应用详情响应时发生错误: {e}")
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.info(f"处理响应失败，尝试重试 (第 {retry_count}/{max_retries} 次)")
                        await asyncio.sleep(1)
                        continue
                    return None
            elif response_data["status_code"] == 404:
                logger.error(f"调用 Dify 获取应用详情 API ({app_id}) 失败：应用未找到 (404)。")
                return None
            else:
                try:
                    logger.error(f"调用 Dify 获取应用详情 API ({app_id}) 失败 (状态码 {response_data['status_code']})：{response_data['text']}")
                except Exception as e:
                    logger.error(f"读取错误响应时发生异常：{e}")
                return None

        except Exception as e:
            logger.exception(f"获取应用详情时发生未处理的异常: {e}")
            retry_count += 1
            if retry_count < max_retries:
                logger.info(f"发生异常，尝试重试 (第 {retry_count}/{max_retries} 次)")
                await asyncio.sleep(1)
            else:
                logger.error(f"已达到最大重试次数 ({max_retries})，获取应用详情失败。")
                return None

    return None

async def get_dify_models_service() -> Optional[Dict[str, Any]]:
    """
    获取Dify后端的模型列表，特别是Ollama插件中注册的模型

    Returns:
        Optional[Dict[str, Any]]: 成功则返回模型列表数据，失败返回None
    """
    logger = logging.getLogger(__name__)

    url = f"{settings.DIFY_API_BASE_URL}/console/api/workspaces/current/models/model-types/llm"
    max_retries = 3
    retry_count = 0

    # 使用同步的 requests 库获取模型列表
    def sync_get_models(access_token: str) -> Optional[Dict[str, Any]]:
        headers = {
            "Authorization": f"Bearer {access_token}",
        }
        try:
            # 增加超时时间，因为模型列表可能很大
            response = requests.get(url, headers=headers, timeout=120.0)

            # 处理响应
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    logger.info("成功获取模型列表")

                    # 提取Ollama模型
                    ollama_models = []
                    for provider in response_data.get("data", []):
                        if provider.get("provider") == "langgenius/ollama/ollama":
                            ollama_models = provider.get("models", [])
                            break

                    # 返回包含Ollama模型的结果
                    return {
                        "ollama_models": ollama_models
                    }
                except json.JSONDecodeError as e:
                    logger.error(f"解析模型列表JSON响应时发生错误: {e}")
                    return None
                except Exception as e:
                    logger.exception(f"处理模型列表响应时发生错误: {e}")
                    return None
            elif response.status_code == 401:
                # 返回401状态码，表示需要刷新token
                return {"status_code": 401}
            else:
                try:
                    logger.error(f"获取模型列表失败 (状态码 {response.status_code})：{response.text}")
                except Exception as e:
                    logger.error(f"读取错误响应时发生异常：{e}")
                return None
        except requests.RequestException as e:
            logger.error(f"调用 Dify 获取模型列表 API 网络错误：{e}")
            return None
        except Exception as e:
            logger.exception(f"调用 Dify 获取模型列表 API 时发生未知错误: {e}")
            return None

    # 使用线程池执行同步请求
    while retry_count < max_retries:
        try:
            access_token = await get_dify_console_access_token()
            if not access_token:
                logger.error("无法获取模型列表：获取 Dify access token 失败。")
                return None

            # 使用 asyncio 的 run_in_executor 在线程池中执行同步请求
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, lambda: sync_get_models(access_token))

            if result is None:
                retry_count += 1
                if retry_count < max_retries:
                    logger.info(f"获取模型列表请求失败，尝试重试 (第 {retry_count}/{max_retries} 次)")
                    await asyncio.sleep(1)
                    continue
                return None

            # 处理 Token 过期 (401)
            if isinstance(result, dict) and result.get("status_code") == 401:
                logger.warning("Dify access token 可能已过期，尝试刷新 token 并重试...")
                refresh_successful = await _refresh_dify_token_internal()
                if refresh_successful:
                    logger.info("Token 刷新成功，重试获取模型列表 API...")
                    new_access_token = _dify_console_tokens.get("access_token")
                    if new_access_token:
                        result = await loop.run_in_executor(None, lambda: sync_get_models(new_access_token))
                        if result is None:
                            retry_count += 1
                            if retry_count < max_retries:
                                logger.info(f"Token刷新后获取失败，尝试重试 (第 {retry_count}/{max_retries} 次)")
                                await asyncio.sleep(1)
                                continue
                            return None
                    else:
                        logger.error("刷新 token 后未能获取到新的 access token，无法重试。")
                        return None
                else:
                    logger.error("刷新 Dify token 失败，无法重试获取模型列表。")
                    return None

            # 返回结果
            return result

        except Exception as e:
            logger.exception(f"获取模型列表时发生未处理的异常: {e}")
            retry_count += 1
            if retry_count < max_retries:
                logger.info(f"发生异常，尝试重试 (第 {retry_count}/{max_retries} 次)")
                await asyncio.sleep(1)
            else:
                logger.error(f"已达到最大重试次数 ({max_retries})，获取模型列表失败。")
                return None

    return None

async def get_document_details(dataset_id: str, document_id: str, metadata: str = "without") -> Optional[Dict[str, Any]]:
    """
    获取Dify知识库文档详情

    Args:
        dataset_id: 知识库ID
        document_id: 文档ID
        metadata: 元数据返回方式，可选值为 "with" 或 "without"

    Returns:
        Optional[Dict[str, Any]]: 成功则返回文档详情，失败返回None
    """
    logger = logging.getLogger(__name__)

    url = f"{settings.DIFY_API_BASE_URL}/console/api/datasets/{dataset_id}/documents/{document_id}"
    params = {"metadata": metadata}

    async def attempt_get_details(access_token: str) -> Optional[aiohttp.ClientResponse]:
        headers = {
            "Authorization": f"Bearer {access_token}",
        }
        try:
            async with aiohttp.ClientSession() as session:
                response = await session.get(url, params=params, headers=headers, timeout=30.0)
                return response
        except aiohttp.ClientError as e:
            logger.error(f"调用 Dify 获取文档详情 API 网络错误：{e}")
            return None
        except Exception as e:
            logger.exception(f"调用 Dify 获取文档详情 API 时发生未知错误: {e}")
            return None

    access_token = await get_dify_console_access_token()
    if not access_token:
        logger.error("无法获取文档详情：获取 Dify access token 失败。")
        return None

    response = await attempt_get_details(access_token)
    if response is None:
        return None

    # 处理 Token 过期 (401)
    if response.status == 401:
        logger.warning("Dify access token 可能已过期，尝试刷新 token 并重试...")
        refresh_successful = await _refresh_dify_token_internal()
        if refresh_successful:
            logger.info("Token 刷新成功，重试获取文档详情 API...")
            new_access_token = _dify_console_tokens.get("access_token")
            if new_access_token:
                await response.release()
                response = await attempt_get_details(new_access_token)
                if response is None:
                    return None
            else:
                logger.error("刷新 token 后未能获取到新的 access token，无法重试。")
                await response.release()
                return None
        else:
            logger.error("刷新 Dify token 失败，无法重试获取文档详情。")
            await response.release()
            return None

    # 处理响应
    if response.status == 200:
        try:
            response_data = await response.json()
            logger.info(f"成功获取文档详情: {response_data}")
            return response_data
        except Exception as e:
            logger.exception(f"处理文档详情响应时发生错误: {e}")
            return None
        finally:
            await response.release()
    else:
        try:
            response_text = await response.text()
            logger.error(f"获取文档详情失败 (状态码 {response.status})：{response_text}")
        except Exception as e:
            logger.error(f"读取错误响应时发生异常：{e}")
        finally:
            await response.release()
        return None