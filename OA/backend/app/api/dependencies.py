from fastapi import Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
import logging
import urllib.parse
import asyncio # Import asyncio for the helper function

from app.core.config import settings
from app.db.session import get_db
from app.models.user import User
from app.models.user_dify_app_binding import UserDifyAppBinding # Import the binding model
from app.services.dify_console_service import ( # Import Dify services
    create_dify_app_service,
    get_dify_app_details_service
)

# Header names (consider defining these in config or a constants file)
HEADER_TENANT_USER_ID = "x-tenant-user-id"
HEADER_TENANT_ID = "x-tenant-id"
HEADER_TENANT_USER_NAME = "x-tenant-user-name"
# HEADER_TENANT_NAME = "x-tenant-name" # If needed for user creation

# Define a Pydantic model for the user info we get/create (optional but good practice)
# You might already have a User schema in app.schemas
# from pydantic import BaseModel, Field
# class TenantUserInfo(BaseModel):
#     id: int # Local DB User ID
#     external_user_id: str
#     username: str
#     tenant_id: str
#     is_active: bool
#     is_superuser: bool # Keep this for now, or adapt based on external roles header
#
#     class Config:
#         orm_mode = True

logger = logging.getLogger(__name__) # Get logger instance

# --- Helper function to create and bind Dify app ---
async def _create_and_bind_dify_app_for_user(db: Session, user: User):
    """Helper function to handle Dify app creation and DB binding."""
    # Use external_user_id for Dify app name to ensure uniqueness across tenants if needed
    dify_app_name = f"oa_user_{user.external_user_id}"
    print(f"[PRINT DEBUG] >>> ENTERING _create_and_bind_dify_app_for_user for user {user.id}, app name: {dify_app_name}")

    try:
        # 1. Create Dify App
        print(f"[PRINT DEBUG] Calling create_dify_app_service for {dify_app_name}")
        creation_result = await create_dify_app_service(
            name=dify_app_name,
            # Add other default params if needed, e.g., description
        )
        print(f"[PRINT DEBUG] create_dify_app_service result: {creation_result}")

        if not creation_result or "id" not in creation_result:
            print(f"[PRINT ERROR] Failed to create Dify app or missing ID for user {user.id}")
            return # Allow user login/provisioning to continue, but log the failure

        dify_app_id = creation_result["id"]
        print(f"[PRINT DEBUG] Dify app created, ID: {dify_app_id}. Getting details...")

        # 2. Get Dify App Details (to get site access token)
        # Add a small delay before getting details, sometimes Dify needs a moment
        print(f"[PRINT DEBUG] Waiting 1 second before getting details for {dify_app_id}")
        await asyncio.sleep(1)
        print(f"[PRINT DEBUG] Calling get_dify_app_details_service for {dify_app_id}")
        details_result = await get_dify_app_details_service(dify_app_id)
        print(f"[PRINT DEBUG] get_dify_app_details_service result: {details_result}")

        if not details_result or not details_result.get('site') or not details_result['site'].get('access_token'):
            print(f"[PRINT ERROR] Failed to get details or site.access_token for app {dify_app_id}")
            return # Continue user process, log failure

        dify_site_access_token = details_result['site']['access_token']
        print(f"[PRINT DEBUG] Successfully got site access token for app {dify_app_id}")

        # 3. Create DB Binding
        print(f"[PRINT DEBUG] Checking for existing binding for user {user.id}")
        existing_binding = db.query(UserDifyAppBinding).filter(UserDifyAppBinding.user_id == user.id).first()
        if existing_binding:
            print(f"[PRINT WARNING] Binding already exists for user {user.id}, skipping creation.")
            return

        print(f"[PRINT DEBUG] Creating new UserDifyAppBinding object for user {user.id}, app {dify_app_id}")
        new_binding = UserDifyAppBinding(
            user_id=user.id,
            dify_app_id=dify_app_id,
            dify_site_access_token=dify_site_access_token
        )
        print(f"[PRINT DEBUG] Adding new binding to session for user {user.id}")
        db.add(new_binding)
        # logger.info(f"准备为用户 {user.id} 添加 Dify 应用绑定记录 (将在事务结束时提交)。")
        print(f"[PRINT DEBUG] Added binding to session for user {user.id} (will commit later).")

    except Exception as e:
        # logger.exception(f"为用户 {user.id} 创建或绑定 Dify 应用时发生错误: {e}")
        print(f"[PRINT EXCEPTION] Exception in _create_and_bind_dify_app_for_user for user {user.id}: {e}")
        import traceback
        traceback.print_exc() # Print full traceback

async def get_current_user(
    request: Request, # Inject Request to access headers
    db: Session = Depends(get_db)
) -> User: # Keep returning User model for compatibility with existing dependents
    print(">>> ENTERING get_current_user <<<")
    """
    获取当前用户，通过读取网关注入的 Header，并执行 JIT 用户创建。
    不再验证 JWT。
    Requires User model to have 'external_user_id' and 'tenant_id' fields.
    """
    # logging.debug(f"get_current_user received headers: {request.headers}")
    print(f"[PRINT DEBUG] get_current_user headers: {request.headers}")

    tenant_user_id = request.headers.get(HEADER_TENANT_USER_ID)
    tenant_id = request.headers.get(HEADER_TENANT_ID)
    tenant_user_name_raw = request.headers.get(HEADER_TENANT_USER_NAME)
    tenant_user_name = None # Initialize decoded name

    if tenant_user_name_raw:
        try:
            tenant_user_name = urllib.parse.unquote(tenant_user_name_raw, encoding='utf-8')
            # logging.debug(f"Decoded UserName Header: {tenant_user_name}")
        except Exception as e:
            # logging.warning(f"Failed to URL decode {HEADER_TENANT_USER_NAME} header: {e}. Raw value: {tenant_user_name_raw}")
            print(f"[PRINT WARNING] Failed to decode username header: {e}, raw: {tenant_user_name_raw}")
            # Keep tenant_user_name as None if decoding fails
    print(f"[PRINT DEBUG] Extracted Headers - UserID: {tenant_user_id}, TenantID: {tenant_id}, UserName: {tenant_user_name}")

    if not tenant_user_id or not tenant_id:
        # In production, missing headers from the gateway are a critical error
        # In local dev with mock middleware, they should be present if enabled
        # logging.error(f"Missing required headers: {HEADER_TENANT_USER_ID} or {HEADER_TENANT_ID}")
        print(f"[PRINT ERROR] Missing required headers ({HEADER_TENANT_USER_ID} or {HEADER_TENANT_ID})")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="缺少必要的身份认证 Headers",
        )

    # TODO: Implement IP check if needed for security
    # trusted_gateway_ip = settings.TRUSTED_GATEWAY_IP # Get from config
    # client_ip = request.client.host
    # if client_ip != trusted_gateway_ip and not getattr(settings, 'MOCK_GATEWAY_HEADERS', False):
    #     logging.warning(f"Request from untrusted IP {client_ip} blocked.")
    #     raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Request not from trusted source")

    # Try to find user by external_user_id (assuming this field exists in User model)
    # !!! Adapt 'external_user_id' to your actual field name in the User model !!!
    try:
        # logging.debug(f"Querying user with external_user_id = {tenant_user_id}")
        print(f"[PRINT DEBUG] Querying user external_id={tenant_user_id}")
        user = db.query(User).filter(User.external_user_id == tenant_user_id).first()
        # logging.debug(f"User query result: {user}")
        print(f"[PRINT DEBUG] User query result: {'found' if user else 'not found'}")
    except AttributeError as e:
        # logging.error(f"AttributeError during user query: {e}. Check User model fields.", exc_info=True)
        print(f"[PRINT ERROR] AttributeError during user query: {e}. Check User model fields.")
        raise HTTPException(status_code=500, detail="服务器内部错误：用户模型配置不正确")
    except Exception as e:
        # logging.error(f"Unexpected error during user query: {e}", exc_info=True)
        print(f"[PRINT ERROR] Unexpected error during user query: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误：查询用户失败")

    if user is None:
        # User not found - Just-In-Time Provisioning
        print(f"[PRINT INFO] User external_id={tenant_user_id} not found. Provisioning...")
        if not tenant_user_name: # Check the decoded value
            print(f"[PRINT WARNING] Username header missing/invalid, using default.")
            default_user_name = f"user_{tenant_user_id[:12]}" # Create a default username
            username_to_use = default_user_name
            fullname_to_use = default_user_name
        else:
            username_to_use = tenant_user_name
            fullname_to_use = tenant_user_name

        # Create a new user object - adapt fields based on your User model
        # !!! Ensure User model has external_user_id and tenant_id fields !!!
        new_user_data = {
            "external_user_id": tenant_user_id,
            "username": username_to_use, # Use potentially decoded name
            "tenant_id": tenant_id,
            "email": f"{tenant_user_id[:12]}@placeholder.oa", # Placeholder email, ensure validity/uniqueness if needed
            "full_name": fullname_to_use, # Use potentially decoded name
            "is_active": True,
            "is_superuser": False # Default to non-superuser; adjust based on roles header if available
            # Add department, position if available in headers or set defaults
        }
        print(f"[PRINT DEBUG] Provisioning user data: {new_user_data}")

        # Assuming you directly create the model instance
        try:
            # Ensure the User model can accept these fields during initialization
            user = User(**new_user_data)
            print(f"[PRINT DEBUG] Adding new user to session...")
            db.add(user)
            print(f"[PRINT DEBUG] Flushing session to get user ID...")
            db.flush() # Important: We need the user ID for binding, so flush to get it
            print(f"[PRINT DEBUG] Added new user {user.id} (external: {tenant_user_id}) to session. Calling Dify bind helper...")
            # --- Trigger Dify App Creation for New User ---
            await _create_and_bind_dify_app_for_user(db, user)
            # --- Commit User and Binding (if added) ---
            print(f"[PRINT DEBUG] Committing transaction for new user {user.id}...")
            db.commit()
            print(f"[PRINT DEBUG] Refreshing user {user.id}...")
            db.refresh(user) # Refresh to get final state
            print(f"[PRINT INFO] Successfully provisioned/committed user {user.id}")
        except Exception as e:
            db.rollback()
            print(f"[PRINT ERROR] Failed to provision user for external_id {tenant_user_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="无法自动创建用户",
            )
    else:
         # --- Existing User Found ---
         # logging.debug(f"Found existing user {user.id} for external_id {tenant_user_id}")
         print(f"[PRINT DEBUG] Found existing user {user.id} for external_id {tenant_user_id}")
         # Check if Dify binding exists for the existing user
         print(f"[PRINT DEBUG] Checking binding for existing user {user.id}...")
         # 直接查询绑定关系，因为我们移除了 relationship 定义
         binding = db.query(UserDifyAppBinding).filter(UserDifyAppBinding.user_id == user.id).first()
         if binding is None:
             # logger.info(f"用户 {user.id} 存在，但缺少 Dify 应用绑定，尝试创建和绑定...")
             print(f"[PRINT INFO] User {user.id} missing binding. Calling Dify bind helper..." )
             await _create_and_bind_dify_app_for_user(db, user)
             # --- Commit potential new binding ---
             # Important: Only commit if the helper function likely added something.
             # We need to commit separately here because the user object itself wasn't new.
             try:
                 print(f"[PRINT DEBUG] Committing transaction for existing user {user.id} binding (if added)...")
                 db.commit()
                 print(f"[PRINT DEBUG] Refreshing user {user.id} after potential binding commit..." )
                 db.refresh(user) # Refresh user to load the binding if successful
             except Exception as e:
                 db.rollback()
                 print(f"[PRINT ERROR] Failed to commit/refresh after attempting to bind Dify app for existing user {user.id}: {e}")
                 # Decide if this failure should prevent user access or just be logged
                 # For now, log and continue, but the binding might be missing.
         else:
             # logging.debug(f"用户 {user.id} 已有关联的 Dify 应用 ID: {user.dify_app_binding.dify_app_id}")
             print(f"[PRINT DEBUG] User {user.id} already has Dify app binding: {binding.dify_app_id}")

    # Now 'user' is either the found user or the newly created user
    print(f"[PRINT DEBUG] get_current_user returning user object: {user}") # Be careful logging sensitive info
    return user

# These functions now depend on the *new* get_current_user which reads headers
def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    获取当前活跃用户 (依赖于从Header获取的用户信息)
    """
    if not current_user.is_active:
        # You might want to check activation status from an external source too
        raise HTTPException(status_code=400, detail="用户未激活")
    return current_user

def get_current_active_superuser(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    获取当前活跃超级用户 (依赖于从Header获取的用户信息)
    权限判断逻辑可能需要调整，例如检查 Header 中的角色信息
    """
    # TODO: Adapt superuser logic. Maybe check roles from header?
    # roles_header = request.headers.get("x-user-roles", "") # Need request here or pass roles through user object/context
    # if "admin" not in roles_header.split(','):
    if not current_user.is_superuser: # Keep simple check for now, assuming JIT sets it correctly or it's managed elsewhere
        raise HTTPException(
            status_code=403, detail="用户权限不足 (非超级管理员)"
        )
    return current_user
