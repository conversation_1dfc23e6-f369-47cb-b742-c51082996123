from fastapi import APIRouter

from app.api.v1.endpoints import workflows
from app.api.v1.endpoints import resources
from app.api.v1.endpoints import processes  # 导入v1版本的processes路由
from app.api.v1.endpoints import knowledge  # 导入知识库路由
from app.api.v1.endpoints import knowledge_permissions  # 导入知识库权限路由
from app.api.v1.endpoints import knowledge_tags  # 导入知识库标签路由
from app.api.v1.endpoints import dify_management  # 导入 Dify 管理路由
from app.api.v1.endpoints import users  # 导入用户路由
from app.api.v1.endpoints import ai_analysis  # 导入AI分析路由

api_router = APIRouter()

# 添加工作流路由
api_router.include_router(workflows.router, prefix="/workflows", tags=["workflows"])

# 添加资源路由（文件上传等）
api_router.include_router(resources.router, prefix="/resources", tags=["resources"])

# 添加流程路由（v1版本）
api_router.include_router(processes.router, prefix="/processes", tags=["流程管理"])

# 添加知识库路由
api_router.include_router(knowledge.router, prefix="/knowledge", tags=["知识库"])
api_router.include_router(knowledge_permissions.router, prefix="/knowledge", tags=["知识库权限"])
api_router.include_router(knowledge_tags.router, prefix="/knowledge", tags=["知识库标签"])

# 添加 Dify 管理路由
api_router.include_router(dify_management.router, prefix="/dify-management", tags=["Dify 管理"])

# 添加用户路由
api_router.include_router(users.router, prefix="/users", tags=["users"])

# 添加AI分析路由
api_router.include_router(ai_analysis.router, prefix="/ai-analysis", tags=["AI分析"])
