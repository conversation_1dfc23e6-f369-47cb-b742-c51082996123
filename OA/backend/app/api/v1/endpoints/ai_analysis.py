"""
AI分析流程API
用于接收用户输入，调用大模型进行分析并自动匹配流程模板
"""

from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session
from typing import Any, Dict, List, Optional
import json
import logging
import requests
from pydantic import BaseModel

from app.api import dependencies as deps
from app.models.user import User
from app.models.process import ProcessTemplate
from app.core.config import settings

# 初始化日志
logger = logging.getLogger(__name__)

router = APIRouter()

class AnalysisRequest(BaseModel):
    """流程分析请求"""
    user_input: str

class AnalysisResponse(BaseModel):
    """流程分析响应"""
    success: bool
    template_id: Optional[int] = None
    template_name: Optional[str] = None
    form_data: Dict[str, Any] = {}
    message: Optional[str] = None

@router.post("/analyze", response_model=AnalysisResponse)
async def analyze_process_input(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    analysis_req: AnalysisRequest,
) -> Any:
    """分析用户输入，智能匹配流程模板并返回自动填充的表单数据"""
    logger.info(f"收到流程分析请求: {analysis_req.user_input[:100]}...")
    
    try:
        # 1. 获取所有活跃的流程模板
        templates = (
            db.query(ProcessTemplate)
            .filter(ProcessTemplate.is_active == True)
            .all()
        )
        
        if not templates:
            logger.warning("没有找到活跃的流程模板")
            return AnalysisResponse(
                success=False,
                message="系统中没有可用的流程模板"
            )
        
        # 2. 解析模板数据，将JSON字符串转为对象
        template_data = []
        for template in templates:
            form_schema = template.form_schema
            if isinstance(form_schema, str):
                try:
                    form_schema = json.loads(form_schema)
                except json.JSONDecodeError:
                    logger.error(f"模板 {template.id} 的form_schema解析失败")
                    continue
                    
            steps = template.steps
            if isinstance(steps, str):
                try:
                    steps = json.loads(steps)
                except json.JSONDecodeError:
                    steps = {}
            
            template_data.append({
                "id": template.id,
                "name": template.name,
                "description": template.description,
                "type": template.type,
                "department": template.department,
                "form_schema": form_schema,
                "steps": steps
            })
        
        # 3. 调用通义千问大模型进行分析
        result = call_qwen_model(analysis_req.user_input, template_data)
        
        # 4. 返回分析结果
        return result
        
    except Exception as e:
        logger.exception("流程分析过程中发生错误")
        return AnalysisResponse(
            success=False,
            message=f"分析失败: {str(e)}"
        )

def call_qwen_model(user_input: str, templates: List[Dict]) -> AnalysisResponse:
    """调用通义千问大模型进行分析"""
    try:
        # 配置API请求参数
        api_key = settings.DASHSCOPE_API_KEY
        if not api_key:
            logger.error("未配置DASHSCOPE_API_KEY环境变量")
            return AnalysisResponse(
                success=False,
                message="系统未配置AI分析所需的API密钥"
            )
            
        url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        
        # 准备提示词和模板数据
        templates_json = json.dumps(templates, ensure_ascii=False)
        
        # 构建系统提示词
        system_prompt = f"""
        你是一个专业的智能OA系统分析助手，负责根据用户输入自动识别合适的流程模板并填充表单数据。
        
        请根据用户的输入内容，从以下预定义的流程模板中，选择最匹配的一个模板，并从用户输入中提取出相关信息填充到模板字段中。
        
        你只能输出JSON格式的分析结果，必须包含以下字段:
        1. "template_id": 选中模板的ID
        2. "template_name": 选中模板的名称
        3. "form_data": 包含所有从用户输入中提取出的表单字段值
        
        流程模板数据: {templates_json}
        """
        
        # 构建用户指令和输入
        user_prompt = f"""
        请分析以下用户输入，识别最匹配的流程模板，并提取信息填充表单字段：
        
        {user_input}
        
        只返回JSON格式结果，不要有任何其他解释。
        """
        
        # 构建API请求
        payload = {
            "model": "qwen-turbo-latest",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "extra_body": {"enable_thinking": False},
            "temperature": 0.2,  # 降低随机性，提高确定性
            "top_p": 0.9,
            "max_tokens": 2000
        }
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        # 发送请求
        logger.info("正在调用通义千问大模型...")
        response = requests.post(url, headers=headers, json=payload)
        
        if response.status_code != 200:
            logger.error(f"大模型API调用失败: {response.status_code}, {response.text}")
            return AnalysisResponse(
                success=False,
                message=f"AI分析服务调用失败: {response.status_code}"
            )
            
        # 解析API响应
        result = response.json()
        logger.debug(f"大模型返回结果: {result}")
        
        # 提取模型回复
        ai_response = result.get("choices", [{}])[0].get("message", {}).get("content", "")
        logger.info(f"大模型分析结果: {ai_response[:100]}...")
        
        # 解析JSON响应
        try:
            # 尝试清理可能的markdown格式
            if "```json" in ai_response:
                ai_response = ai_response.split("```json")[1].split("```")[0].strip()
            elif "```" in ai_response:
                ai_response = ai_response.split("```")[1].split("```")[0].strip()
                
            model_result = json.loads(ai_response)
            
            # 验证模板ID是否存在于提供的模板列表中
            template_id = model_result.get("template_id")
            template_exists = any(template["id"] == template_id for template in templates)
            
            if not template_exists:
                logger.warning(f"大模型返回的模板ID {template_id} 不在有效模板列表中")
                return AnalysisResponse(
                    success=False,
                    message=f"AI返回了无效的模板ID: {template_id}"
                )
                
            # 构建成功响应
            return AnalysisResponse(
                success=True,
                template_id=model_result.get("template_id"),
                template_name=model_result.get("template_name"),
                form_data=model_result.get("form_data", {})
            )
            
        except json.JSONDecodeError as e:
            logger.error(f"解析大模型返回的JSON失败: {e}, 原始响应: {ai_response}")
            return AnalysisResponse(
                success=False,
                message="AI返回的格式无法解析，请重试"
            )
            
    except Exception as e:
        logger.exception(f"调用大模型过程中发生错误: {e}")
        return AnalysisResponse(
            success=False,
            message=f"AI分析失败: {str(e)}"
        )
