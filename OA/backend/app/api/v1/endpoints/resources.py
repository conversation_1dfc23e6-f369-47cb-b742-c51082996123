from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import uuid
import shutil
import logging
import traceback
from datetime import datetime

from app.api import dependencies as deps
from app.models.user import User
from app.models.file import File as FileModel
from app.schemas.file import FileResponse
from app.core.config import settings
from app.db.session import SessionLocal

# 配置日志
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

router = APIRouter()

def save_upload_file(upload_file: UploadFile, destination: str):
    """保存上传的文件到指定路径"""
    with open(destination, "wb") as buffer:
        shutil.copyfileobj(upload_file.file, buffer)

    # 获取文件大小
    file_size = os.path.getsize(destination)
    return file_size

@router.post("/upload", response_model=FileResponse)
async def upload_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    process_id: Optional[int] = None,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
):
    """上传文件

    Args:
        file: 上传的文件
        process_id: 关联的流程ID（可选）

    Returns:
        JSON包含文件ID和访问URL
    """
    # 检查文件类型
    allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'}
    file_ext = file.filename.split(".")[-1].lower() if "." in file.filename else ""

    if file_ext not in allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的文件类型，允许的类型: {', '.join(allowed_extensions)}"
        )

    try:
        logger.info(f"开始处理文件上传: {file.filename}, process_id: {process_id}")
        # 获取用户ID
        user_id = current_user.id
        logger.info(f"当前用户ID: {user_id if user_id else 'None'}")

        # 打印上传目录信息
        logger.info(f"UPLOAD_DIR配置: {settings.UPLOAD_DIR}")
        if not os.path.exists(settings.UPLOAD_DIR):
            logger.error(f"上传目录不存在: {settings.UPLOAD_DIR}")
            os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
            logger.info(f"已创建上传目录: {settings.UPLOAD_DIR}")

        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4().hex}_{file.filename}"
        logger.info(f"生成的唯一文件名: {unique_filename}")

        # 确保上传目录存在
        today = datetime.now().strftime('%Y%m%d')
        upload_dir = os.path.join(settings.UPLOAD_DIR, today)
        os.makedirs(upload_dir, exist_ok=True)
        logger.info(f"日期目录已创建: {upload_dir}")

        # 完整文件路径
        file_path = os.path.join(upload_dir, unique_filename)
        logger.info(f"完整文件路径: {file_path}")

        # 在后台任务中保存文件并获取文件大小
        file_size = 0
        try:
            # 同步保存文件，以便能获取文件大小
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)

            # 获取文件大小
            file_size = os.path.getsize(file_path)
            logger.info(f"文件保存成功，大小: {file_size}字节")
        except Exception as e:
            logger.error(f"文件保存失败: {str(e)}")
            logger.error(traceback.format_exc())
            raise HTTPException(status_code=500, detail=f"文件保存失败: {str(e)}")

        # 保存文件信息到数据库
        try:
            logger.info("开始保存文件信息到数据库")
            # 获取用户ID
            user_id = current_user.id

            # 开发/测试环境下，如果无法获取用户ID，使用默认值1
            if not user_id:
                logger.warning("无法获取用户ID，使用默认值1（仅在开发/测试环境下）")
                user_id = 1

            file_model = FileModel(
                original_name=file.filename,
                stored_name=unique_filename,
                path=file_path,
                size=file_size,
                file_type=file_ext,
                uploader_id=user_id,
                process_id=process_id
            )

            db.add(file_model)
            db.commit()
            db.refresh(file_model)
            logger.info(f"文件信息已保存到数据库，ID: {file_model.id}")
        except Exception as e:
            logger.error(f"数据库操作失败: {str(e)}")
            logger.error(traceback.format_exc())
            # 如果数据库操作失败，删除已上传的文件
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"已删除文件: {file_path}")
            raise HTTPException(status_code=500, detail=f"保存文件信息失败: {str(e)}")

        # 构建文件访问路径
        file_url = f"/api/v1/resources/files/{file_model.id}"

        return {
            "success": True,
            "file_id": file_model.id,
            "file_name": file.filename,
            "file_url": file_url,
            "file_type": file_ext,
            "file_size": file_size
        }

    except Exception as e:
        logger.error(f"文件上传过程发生未捕获异常: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@router.get("/files/{file_id}", response_model=FileResponse)
def get_file_info(
    file_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
):
    """获取文件信息

    Args:
        file_id: 文件ID

    Returns:
        文件信息JSON
    """
    file_model = db.query(FileModel).filter(FileModel.id == file_id).first()

    if not file_model:
        raise HTTPException(status_code=404, detail="文件不存在")

    # 简单权限验证：只允许上传者或同部门用户访问
    if (file_model.uploader_id != current_user.id and
        file_model.uploader.department != current_user.department):
        raise HTTPException(status_code=403, detail="无权访问此文件")

    return {
        "success": True,
        "file_id": file_model.id,
        "file_name": file_model.original_name,
        "file_type": file_model.file_type,
        "file_size": file_model.size,
        "upload_time": file_model.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        "uploader": file_model.uploader.username,
        "download_url": f"/api/v1/resources/files/{file_model.id}/download"
    }

@router.get("/files/{file_id}/download")
def download_file(
    file_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
):
    """下载文件

    Args:
        file_id: 文件ID

    Returns:
        文件内容
    """
    file_model = db.query(FileModel).filter(FileModel.id == file_id).first()

    if not file_model:
        raise HTTPException(status_code=404, detail="文件不存在")

    # 简单权限验证：只允许上传者或同部门用户下载
    if (file_model.uploader_id != current_user.id and
        file_model.uploader.department != current_user.department):
        raise HTTPException(status_code=403, detail="无权下载此文件")

    if not os.path.exists(file_model.path):
        raise HTTPException(status_code=404, detail="文件不存在于服务器")

    return FileResponse(
        path=file_model.path,
        filename=file_model.original_name,
        media_type="application/octet-stream"
    )
