from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Body, Path, UploadFile, File, Form, BackgroundTasks
from sqlalchemy.orm import Session
import uuid
import json
from datetime import datetime, timedelta
import logging
import asyncio
import time

from app import crud, schemas, models
from app.models.user import User
from app.api import dependencies as deps
from app.schemas.knowledge import (
    KnowledgeProviderResponse, KnowledgeResponse, DocumentResponse,
    KnowledgeBaseCreate, KnowledgeBaseUpdate,
    DocumentCreate, DocumentUpdate,DocumentStatus,DocumentType,
    SearchQuery, SearchResult,
    KnowledgeStats, ShareInfo, ShareRequest,
    PermissionCreate, PermissionResponse, PermissionType
)
from app.core.security import get_password_hash
from app.core.api_security import encrypt_api_key

# 使用知识库服务工厂获取对应的知识库服务
from app.services.knowledge.factory import get_knowledge_service

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/providers", response_model=List[KnowledgeProviderResponse])
def get_knowledge_providers(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = True
) -> Any:
    """
    获取知识库供应商列表
    """
    providers = crud.knowledge_provider.get_multi(
        db, skip=skip, limit=limit, is_active=is_active
    )
    return providers


@router.post("/providers", response_model=KnowledgeProviderResponse)
def create_knowledge_provider(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    provider_in: schemas.knowledge.KnowledgeProviderCreate
) -> Any:
    """
    创建知识库供应商
    """
    # 检查用户权限（只有超级管理员可以创建供应商）
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="Not enough permissions"
        )

    # 检查是否已存在同名供应商
    existing_provider = crud.knowledge_provider.get_by_name(db, name=provider_in.name)
    if existing_provider:
        raise HTTPException(
            status_code=400,
            detail="Provider with this name already exists"
        )

    # 如果有API密钥，对其进行加密
    provider_data = provider_in.dict()
    if provider_data.get("api_key"):
        provider_data["api_key"] = encrypt_api_key(provider_data["api_key"])

    # 创建供应商
    provider = crud.knowledge_provider.create(db, obj_in=provider_data)

    # 记录审计日志
    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id,
        action="create_knowledge_provider",
        details={"name": provider.name, "provider_type": provider.provider_type}
    )

    return provider


@router.put("/providers/{provider_id}", response_model=KnowledgeProviderResponse)
def update_knowledge_provider(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    provider_id: int = Path(..., title="供应商ID"),
    provider_in: schemas.knowledge.KnowledgeProviderUpdate
) -> Any:
    """
    更新知识库供应商
    """
    # 检查用户权限（只有超级管理员可以更新供应商）
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="Not enough permissions"
        )

    # 获取供应商
    provider = crud.knowledge_provider.get(db, provider_id=provider_id)
    if not provider:
        raise HTTPException(
            status_code=404,
            detail="Knowledge provider not found"
        )

    # 如果更新中包含API密钥，对其进行加密
    update_data = provider_in.dict(exclude_unset=True)
    if "api_key" in update_data and update_data["api_key"]:
        update_data["api_key"] = encrypt_api_key(update_data["api_key"])

    # 更新供应商
    provider = crud.knowledge_provider.update(db, db_obj=provider, obj_in=update_data)

    # 记录审计日志
    # 不在日志中记录API密钥相关信息
    log_details = {"id": provider.id, "name": provider.name}
    log_update_fields = provider_in.dict(exclude_unset=True)
    if "api_key" in log_update_fields:
        log_update_fields["api_key"] = "[REDACTED]"
    log_details["updated_fields"] = log_update_fields

    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id,
        action="update_knowledge_provider",
        details=log_details
    )

    return provider


@router.delete("/providers/{provider_id}", response_model=KnowledgeProviderResponse)
def delete_knowledge_provider(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    provider_id: int = Path(..., title="供应商ID")
) -> Any:
    """
    删除知识库供应商
    """
    # 检查用户权限（只有超级管理员可以删除供应商）
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="Not enough permissions"
        )

    # 获取供应商
    provider = crud.knowledge_provider.get(db, provider_id=provider_id)
    if not provider:
        raise HTTPException(
            status_code=404,
            detail="Knowledge provider not found"
        )

    # 检查是否有知识库使用该供应商
    knowledge_bases = db.query(models.KnowledgeBase).filter(models.KnowledgeBase.provider_id == provider_id).all()
    if knowledge_bases:
        raise HTTPException(
            status_code=400,
            detail="Cannot delete provider with associated knowledge bases"
        )

    # 记录审计日志
    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id,
        action="delete_knowledge_provider",
        details={"id": provider.id, "name": provider.name}
    )

    # 删除供应商
    provider = crud.knowledge_provider.delete(db, provider_id=provider_id)

    return provider


@router.post("/bases", response_model=KnowledgeResponse)
async def create_knowledge_base(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    kb_in: KnowledgeBaseCreate
) -> Any:
    """
    创建新的知识库
    """
    # 检查提供商是否存在
    provider = crud.knowledge_provider.get(db, provider_id=kb_in.provider_id)
    if not provider:
        raise HTTPException(
            status_code=404,
            detail="Knowledge provider not found"
        )

    try:
        knowledge_service = get_knowledge_service(provider)
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )

    # 调用知识库服务创建远程知识库
    remote_result = await knowledge_service.create_dataset(
        name=kb_in.name,
        description=kb_in.description
    )

    if not remote_result.get("success"):
        raise HTTPException(
            status_code=remote_result.get("status_code", 500),
            detail=f"Failed to create knowledge base in provider: {remote_result.get('error')}"
        )

    # 获取远程知识库ID
    remote_kb_data = remote_result.get("data", {})
    remote_kb_id = remote_kb_data.get("id")

    if not remote_kb_id:
        raise HTTPException(
            status_code=500,
            detail="Provider did not return a valid knowledge base ID"
        )

    # 在本地数据库创建知识库，并保存远程ID
    kb_in_dict = kb_in.dict()
    kb_in_dict["external_id"] = remote_kb_id  # 保存远程知识库ID到external_id字段

    # 创建本地知识库记录
    kb = crud.knowledge_base.create(db, obj_in=KnowledgeBaseCreate(**kb_in_dict), owner_id=current_user.id)

    # 记录审计日志
    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id, kb_id=kb.id,
        action="create_knowledge_base",
        details={"name": kb.name, "external_id": remote_kb_id}
    )

    # 新创建的知识库没有文档
    document_count = 0

    # 使用创建知识库时传入的标签
    tags = []
    if hasattr(kb_in, 'tags') and kb_in.tags:
        # 如果需要获取完整的标签信息，可以根据ID查询
        tags = [crud.knowledge_tag.get(db, tag_id=tag_id) for tag_id in kb_in.tags if tag_id]
        # 移除可能的None值（如果标签ID不存在）
        tags = [tag for tag in tags if tag]

    # 使用工具函数创建标准化的知识库响应
    from app.utils.response_utils import create_knowledge_response
    response = create_knowledge_response(db, kb, document_count, tags)

    return response


@router.get("/bases", response_model=List[KnowledgeResponse])
def get_knowledge_bases(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None
) -> Any:
    """
    获取知识库列表
    """
    # 根据搜索条件获取知识库
    if search:
        kbs = crud.knowledge_base.search(
            db, search_term=search, owner_id=current_user.id,
            skip=skip, limit=limit
        )
    else:
        kbs = crud.knowledge_base.get_multi(
            db, owner_id=current_user.id, skip=skip, limit=limit
        )

    # 构建响应
    result = []
    for kb in kbs:
        # 获取提供商
        provider = crud.knowledge_provider.get(db, provider_id=kb.provider_id)

        # 获取文档数量
        document_count = crud.knowledge_base.get_document_count(db, kb_id=kb.id)

        # 获取标签
        tags = crud.knowledge_tag.get_by_knowledge_base(db, kb_id=kb.id)

        result.append(
            KnowledgeResponse(
                **kb.__dict__,
                document_count=document_count,
                provider=provider,
                tags=tags
            )
        )

    return result


@router.get("/bases/{kb_id}", response_model=KnowledgeResponse)
def get_knowledge_base(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    kb_id: int = Path(..., title="知识库ID")
) -> Any:
    """
    获取知识库详情
    """
    # 获取知识库
    kb = crud.knowledge_base.get(db, kb_id=kb_id)
    if not kb:
        raise HTTPException(
            status_code=404,
            detail="Knowledge base not found"
        )

    # 检查权限
    if kb.owner_id != current_user.id and not kb.is_public:
        # 检查是否有权限
        permission = crud.knowledge_permission.get_by_user(
            db, kb_id=kb_id, user_id=current_user.id
        )
        if not permission:
            raise HTTPException(
                status_code=403,
                detail="Not enough permissions"
            )

    # 获取提供商
    provider = crud.knowledge_provider.get(db, provider_id=kb.provider_id)

    # 获取文档数量
    document_count = crud.knowledge_base.get_document_count(db, kb_id=kb.id)

    # 获取标签
    tags = crud.knowledge_tag.get_by_knowledge_base(db, kb_id=kb.id)

    # 记录审计日志
    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id, kb_id=kb.id,
        action="view_knowledge_base",
        details={"name": kb.name}
    )

    return KnowledgeResponse(
        **kb.__dict__,
        document_count=document_count,
        provider=provider,
        tags=tags
    )


@router.put("/bases/{kb_id}", response_model=KnowledgeResponse)
async def update_knowledge_base(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    kb_id: int = Path(..., title="知识库ID"),
    kb_in: KnowledgeBaseUpdate
) -> Any:
    """
    更新知识库
    """
    # 获取知识库
    kb = crud.knowledge_base.get(db, kb_id=kb_id)
    if not kb:
        raise HTTPException(
            status_code=404,
            detail="Knowledge base not found"
        )

    # 检查权限
    if kb.owner_id != current_user.id:
        # 检查是否有管理权限
        permission = crud.knowledge_permission.get_by_user(
            db, kb_id=kb_id, user_id=current_user.id
        )
        if not permission or permission.permission_type != PermissionType.ADMIN.value:
            raise HTTPException(
                status_code=403,
                detail="Not enough permissions"
            )

    # 获取提供商
    provider = crud.knowledge_provider.get(db, provider_id=kb.provider_id)
    if not provider:
        raise HTTPException(
            status_code=404,
            detail="Knowledge provider not found"
        )

    # 如果有远程ID，则调用知识库服务更新远程知识库
    if kb.external_id and (kb_in.name is not None or kb_in.description is not None):
        try:
            knowledge_service = get_knowledge_service(provider)
        except ValueError as e:
            raise HTTPException(
                status_code=400,
                detail=str(e)
            )

        # 调用知识库服务更新远程知识库
        remote_result = await knowledge_service.update_dataset(
            dataset_id=kb.external_id,
            name=kb_in.name,
            description=kb_in.description
        )

        if not remote_result.get("success"):
            raise HTTPException(
                status_code=remote_result.get("status_code", 500),
                detail=f"Failed to update knowledge base in provider: {remote_result.get('error')}"
            )

    # 更新本地知识库
    kb = crud.knowledge_base.update(db, db_obj=kb, obj_in=kb_in)

    # 记录审计日志
    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id, kb_id=kb.id,
        action="update_knowledge_base",
        details={"name": kb.name, "updated_fields": kb_in.dict(exclude_unset=True)}
    )

    # 获取文档数量
    document_count = crud.knowledge_base.get_document_count(db, kb_id=kb.id)

    # 获取标签
    tags = crud.knowledge_tag.get_by_knowledge_base(db, kb_id=kb.id)

    return KnowledgeResponse(
        **kb.__dict__,
        document_count=document_count,
        provider=provider,
        tags=tags
    )


@router.delete("/bases/{kb_id}", response_model=KnowledgeResponse)
async def delete_knowledge_base(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    kb_id: int = Path(..., title="知识库ID")
) -> Any:
    """
    删除知识库
    """
    # 获取知识库
    kb = crud.knowledge_base.get(db, kb_id=kb_id)
    if not kb:
        raise HTTPException(
            status_code=404,
            detail="Knowledge base not found"
        )

    # 检查权限
    if kb.owner_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Not enough permissions"
        )

    # 获取提供商
    provider = crud.knowledge_provider.get(db, provider_id=kb.provider_id)
    if not provider:
        raise HTTPException(
            status_code=404,
            detail="Knowledge provider not found"
        )

    # 如果有远程ID，则调用知识库服务删除远程知识库
    if kb.external_id:
        # 使用知识库服务工厂获取对应的知识库服务
        try:
            knowledge_service = get_knowledge_service(provider)
        except ValueError as e:
            raise HTTPException(
                status_code=400,
                detail=str(e)
            )

        # 调用知识库服务删除远程知识库
        remote_result = await knowledge_service.delete_dataset(
            dataset_id=kb.external_id
        )

        # 即使远程删除失败，也继续删除本地知识库，但记录错误
        if not remote_result.get("success"):
            # 记录错误信息
            crud.knowledge_audit_log.create_log(
                db, user_id=current_user.id, kb_id=kb.id,
                action="delete_knowledge_base_remote_error",
                details={
                    "name": kb.name,
                    "external_id": kb.external_id,
                    "error": remote_result.get("error")
                }
            )

    # 记录审计日志
    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id, kb_id=kb.id,
        action="delete_knowledge_base",
        details={"name": kb.name}
    )

    # 获取文档数量
    document_count = crud.knowledge_base.get_document_count(db, kb_id=kb.id)

    # 获取标签
    tags = crud.knowledge_tag.get_by_knowledge_base(db, kb_id=kb.id)

    # 删除本地知识库
    kb = crud.knowledge_base.delete(db, kb_id=kb_id)

    # 使用工具函数创建标准化的知识库响应
    from app.utils.response_utils import create_knowledge_response
    return create_knowledge_response(db, kb, document_count, tags)


@router.post("/bases/{kb_id}/share", response_model=ShareInfo)
def share_knowledge_base(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    kb_id: int = Path(..., title="知识库ID"),
    share_in: ShareRequest
) -> Any:
    """
    分享知识库
    """
    # 获取知识库
    kb = crud.knowledge_base.get(db, kb_id=kb_id)
    if not kb:
        raise HTTPException(
            status_code=404,
            detail="Knowledge base not found"
        )

    # 检查权限
    if kb.owner_id != current_user.id:
        # 检查是否有管理权限
        permission = crud.knowledge_permission.get_by_user(
            db, kb_id=kb_id, user_id=current_user.id
        )
        if not permission or permission.permission_type != PermissionType.ADMIN.value:
            raise HTTPException(
                status_code=403,
                detail="Not enough permissions"
            )

    # 生成分享ID
    share_id = str(uuid.uuid4())

    # 计算过期时间
    expires_at = None
    if share_in.expire_days:
        expires_at = datetime.now() + timedelta(days=share_in.expire_days)

    # 生成分享URL
    share_url = f"/knowledge/share/{share_id}"

    # 如果需要密码保护，则生成密码哈希
    password_hash = None
    if share_in.password_protected and share_in.password:
        password_hash = get_password_hash(share_in.password)

    # 更新知识库配置
    share_config = kb.config.copy() if kb.config else {}
    share_config["share"] = {
        "id": share_id,
        "expires_at": expires_at.isoformat() if expires_at else None,
        "password_protected": share_in.password_protected,
        "password_hash": password_hash
    }

    crud.knowledge_base.update(
        db, db_obj=kb,
        obj_in={"config": share_config}
    )

    # 记录审计日志
    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id, kb_id=kb.id,
        action="share_knowledge_base",
        details={
            "share_id": share_id,
            "expires_at": expires_at.isoformat() if expires_at else None,
            "password_protected": share_in.password_protected
        }
    )

    return ShareInfo(
        share_id=share_id,
        share_url=share_url,
        expires_at=expires_at
    )


@router.get("/bases/{kb_id}/stats", response_model=KnowledgeStats)
def get_knowledge_base_stats(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    kb_id: int = Path(..., title="知识库ID")
) -> Any:
    """
    获取知识库统计信息
    """
    # 获取知识库
    kb = crud.knowledge_base.get(db, kb_id=kb_id)
    if not kb:
        raise HTTPException(
            status_code=404,
            detail="Knowledge base not found"
        )

    # 检查权限
    if kb.owner_id != current_user.id and not kb.is_public:
        # 检查是否有权限
        permission = crud.knowledge_permission.get_by_user(
            db, kb_id=kb_id, user_id=current_user.id
        )
        if not permission:
            raise HTTPException(
                status_code=403,
                detail="Not enough permissions"
            )

    # 获取统计信息
    stats = crud.knowledge_base.get_stats(db, kb_id=kb_id)

    return KnowledgeStats(**stats)


async def check_document_status(
    db: Session,
    doc_id: int,
    kb_external_id: str,
    external_doc_id: str,
    provider: models.KnowledgeProvider,
    max_retries: int = 300,  # 最多重试30次
    retry_interval: int = 10  # 每次重试间隔10秒
):
    """
    后台任务：检查文档处理状态

    Args:
        db: 数据库会话
        doc_id: 文档ID
        kb_external_id: 知识库在外部系统中的ID
        external_doc_id: 外部文档ID
        provider: 知识库提供商
        max_retries: 最大重试次数
        retry_interval: 重试间隔（秒）
    """
    logger = logging.getLogger(__name__)
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 获取知识库服务实例
            knowledge_service = get_knowledge_service(provider)

            # 调用Dify API获取文档状态
            result = await knowledge_service.get_document(
                dataset_id=kb_external_id,
                document_id=external_doc_id
            )

            if not result.get("success"):
                logger.error(f"获取文档状态失败: {result.get('error')}")
                retry_count += 1
                await asyncio.sleep(retry_interval)
                continue

            # 获取文档数据
            doc_data = result.get("data", {})
            indexing_status = doc_data.get("indexing_status")
            error = doc_data.get("error")

            # 更新数据库中的文档状态
            update_data = {}

            # 根据索引状态设置文档状态
            if indexing_status == "completed":
                update_data["status"] = DocumentStatus.COMPLETED.value
            elif indexing_status == "error":
                update_data["status"] = DocumentStatus.FAILED.value
            elif indexing_status in ["indexing", "paused"]:
                update_data["status"] = DocumentStatus.PROCESSING.value

            # 更新错误信息
            if error:
                update_data["error_message"] = error

            # 更新内容预览
            if doc_data.get("content_preview"):
                update_data["content_preview"] = doc_data["content_preview"]

            # 更新元数据
            if doc_data.get("doc_metadata"):
                update_data["doc_metadata"] = doc_data["doc_metadata"]

            # 更新数据库
            doc = crud.knowledge_document.get(db, doc_id=doc_id)
            if doc and update_data:
                crud.knowledge_document.update(
                    db,
                    db_obj=doc,
                    obj_in=update_data
                )
                logger.info(f"文档状态已更新: doc_id={doc_id}, status={update_data.get('status')}, indexing_status={indexing_status}")

            # 如果文档处理完成或失败，结束轮询
            if indexing_status in ["completed", "error"]:
                logger.info(f"文档处理完成或失败，结束轮询: doc_id={doc_id}, status={update_data.get('status')}")
                return

            retry_count += 1
            await asyncio.sleep(retry_interval)

        except Exception as e:
            logger.error(f"检查文档状态时发生错误: {str(e)}")
            retry_count += 1
            await asyncio.sleep(retry_interval)

    # 如果达到最大重试次数，将文档状态设置为失败
    doc = crud.knowledge_document.get(db, doc_id=doc_id)
    if doc:
        crud.knowledge_document.update(
            db,
            db_obj=doc,
            obj_in={
                "status": DocumentStatus.FAILED.value,
                "error_message": "文档处理超时"
            }
        )
        logger.error(f"文档处理超时: doc_id={doc_id}")


@router.post("/bases/{kb_id}/documents", response_model=DocumentResponse)
async def create_document(
    *,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    kb_id: int = Path(..., title="知识库ID"),
    doc_in: DocumentCreate
) -> Any:
    """
    创建文档
    """
    # 获取知识库
    kb = crud.knowledge_base.get(db, kb_id=kb_id)
    if not kb:
        raise HTTPException(
            status_code=404,
            detail="Knowledge base not found"
        )

    # 检查权限
    if kb.owner_id != current_user.id:
        # 检查是否有写入权限
        permission = crud.knowledge_permission.get_by_user(
            db, kb_id=kb_id, user_id=current_user.id
        )
        if not permission or permission.permission_type not in [
            PermissionType.WRITE.value, PermissionType.ADMIN.value
        ]:
            raise HTTPException(
                status_code=403,
                detail="Not enough permissions"
            )

    # 创建文档在本地数据库中
    # 确保设置初始状态为PROCESSING
    doc_in_with_status = doc_in.dict()
    doc_in_with_status["status"] = DocumentStatus.PROCESSING.value

    doc = crud.knowledge_document.create(
        db, obj_in=doc_in_with_status, created_by=current_user.id
    )

    # 记录审计日志
    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id, kb_id=kb_id, doc_id=doc.id,
        action="create_document",
        details={"name": doc.name}
    )

    # 获取知识库提供商
    provider = crud.knowledge_provider.get(db, provider_id=kb.provider_id)
    if not provider:
        raise HTTPException(
            status_code=404,
            detail="Knowledge provider not found"
        )

    # 调用provider API创建文档
    try:
        # 初始化对应的知识库服务
        knowledge_service = get_knowledge_service(provider)

        # 调用API创建文本文档
        if doc_in.document_type == DocumentType.TEXT:
            # 异步调用create_text_document方法
            import asyncio
            import logging
            logger.info(f"调用create_text_document API: dataset_id={kb.external_id}, name={doc_in.name}")
            result = asyncio.run(knowledge_service.create_text_document(
                dataset_id=kb.external_id,
                text=doc_in.content,
                name=doc_in.name,
                metadata=doc_in.doc_metadata
            ))

            # 记录API响应结果便于调试
            logger.info(f"API响应: {result}")

            if not result.get("success"):
                # 更新文档状态为失败
                logger.error(f"API调用失败: {result.get('error')}")
                crud.knowledge_document.update(
                    db,
                    db_obj=doc,
                    obj_in={
                        "status": DocumentStatus.FAILED.value,
                        "error_message": result.get("error", "Unknown error")
                    }
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to create document in provider: {result.get('error')}"
                )

            # 更新文档的external_id和状态
            external_doc_data = result.get("data", {}).get("document", {})
            logger.info(f"API返回的文档数据: {external_doc_data}")

            external_id = external_doc_data.get("id")
            if external_id:
                logger.info(f"更新文档: external_id={external_id}")
                update_result = crud.knowledge_document.update(
                    db,
                    db_obj=doc,
                    obj_in={
                        "external_id": external_id,
                        "status": DocumentStatus.PROCESSING.value,
                        "content_preview": doc_in.content[:200] if doc_in.content else None  # 保存前200个字符作为预览
                    }
                )
                logger.info(f"文档更新结果: external_id={update_result.external_id}, status={update_result.status}")
            else:
                logger.warning(f"API响应中未找到external_id。完整响应: {result}")
                # 即使没有external_id，也将状态更新为处理中
                update_result = crud.knowledge_document.update(
                    db,
                    db_obj=doc,
                    obj_in={
                        "status": DocumentStatus.PROCESSING.value,
                        "content_preview": doc_in.content[:200] if doc_in.content else None
                    }
                )
                logger.info(f"文档状态已更新为处理中: status={update_result.status}")
        else:
            # 对于非文本类型的文档，需要实现其他逻辑
            # 目前仅实现了文本文档的创建
            pass

    except Exception as e:
        # 捕获异常并更新文档状态
        import traceback
        error_detail = traceback.format_exc()
        crud.knowledge_document.update(
            db,
            db_obj=doc,
            obj_in={
                "status": DocumentStatus.FAILED.value,
                "error_message": str(e)
            }
        )
        # 记录错误日志
        crud.knowledge_audit_log.create_log(
            db, user_id=current_user.id, kb_id=kb_id, doc_id=doc.id,
            action="create_document_error",
            details={"name": doc.name, "error": str(e), "error_detail": error_detail}
        )
        raise HTTPException(
            status_code=500,
            detail=f"Error creating document: {str(e)}"
        )

    # 重新获取更新后的文档
    updated_doc = crud.knowledge_document.get(db, doc_id=doc.id)

    # 在成功创建文档后，启动后台任务检查文档状态
    if updated_doc and updated_doc.external_id:
        background_tasks.add_task(
            check_document_status,
            db=db,
            doc_id=updated_doc.id,
            kb_external_id=kb.external_id,
            external_doc_id=updated_doc.external_id,
            provider=provider
        )

    return updated_doc


@router.post("/bases/{kb_id}/documents/upload", response_model=DocumentResponse)
async def upload_document(
    *,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    kb_id: int = Path(..., title="知识库ID"),
    file: UploadFile = File(...),
    name: str = Form(None),
    description: str = Form(None)
) -> Any:
    """
    上传文档到知识库（包括调用外部服务如 Dify）
    """
    # 1. 获取知识库和提供商信息
    kb = crud.knowledge_base.get(db, kb_id=kb_id)
    if not kb:
        raise HTTPException(status_code=404, detail="Knowledge base not found")

    # 直接查询提供商信息，因为我们移除了 relationship 定义
    provider = None
    if kb.provider_id:
        provider = crud.knowledge_provider.get(db, provider_id=kb.provider_id)

    if not provider:
        raise HTTPException(status_code=500, detail="Knowledge base provider configuration missing")

    # 2. 检查权限 (与之前相同)
    if kb.owner_id != current_user.id:
        permission = crud.knowledge_permission.get_by_user(db, kb_id=kb_id, user_id=current_user.id)
        if not permission or permission.permission_type not in [schemas.PermissionType.WRITE.value, schemas.PermissionType.ADMIN.value]:
            raise HTTPException(status_code=403, detail="Not enough permissions")

    # 3. 准备文件信息
    file_name = name or file.filename
    # 暂不读取文件内容，传递 UploadFile 对象给 service
    # content = await file.read() # 移动到 service 中读取
    # file_size = len(content) # 在 service 中计算或由 UploadFile 提供

    # 4. 在 OA DB 中创建初始文档记录 (状态: PROCESSING)
    # 注意：不在此处存储 content，应由外部服务处理和存储
    doc_in = DocumentCreate(
        name=file_name,
        description=description,
        knowledge_base_id=kb_id,
        document_type="file", # 或根据 file.content_type 更精确判断
        content='', # <-- Provide an empty string to satisfy validation
        doc_metadata={
            "original_filename": file.filename,
            "content_type": file.content_type,
            # "size": file_size # size 可选，或由 service 填充
        },
        status=DocumentStatus.PROCESSING.value # 直接在 Pydantic 模型中设置初始状态
    )

    # 使用 .dict() 排除未设置的字段，然后添加状态
    doc_create_data = doc_in.dict(exclude_unset=True)
    doc_create_data["status"] = DocumentStatus.PROCESSING.value

    doc = crud.knowledge_document.create(
        db, obj_in=doc_create_data, created_by=current_user.id
    )
    logger.info(f"已在 OA DB 中创建文档记录 ID: {doc.id}，状态: PROCESSING")

    # 5. 获取知识库服务实例并调用上传
    service_instance = None
    external_doc_id = None # 用于存储 Dify 返回的文档 ID
    upload_success = False
    error_detail_external = "Provider not supported or service initialization failed"

    try:
        logger.info(f"获取知识库服务实例 for provider type: {provider.provider_type}")
        service_instance = get_knowledge_service(provider)

        # 检查获取到的服务实例是否有上传文档的方法
        if hasattr(service_instance, 'upload_document'):
            # 前置检查：所有需要外部上传的服务都必须有 external_id
            if not kb.external_id:
                logger.error(f"知识库 {kb.id} (Provider: {provider.provider_type}) 缺少 external_id，无法上传。")
                raise HTTPException(status_code=400, detail="知识库配置缺少 external_id")

            logger.info(f"调用 {type(service_instance).__name__}.upload_document for OA Doc ID: {doc.id}, External Dataset ID: {kb.external_id}")
            await file.seek(0)
            service_result = await service_instance.upload_document(
                dataset_id=kb.external_id,
                file=file,
                metadata={"oa_document_id": doc.id}
            )
            logger.info(f"外部服务调用返回: {service_result}")

            if service_result.get("success"):
                upload_success = True
                # Try to get the external document ID, specific key might vary by provider
                external_doc_id = service_result.get("data", {}).get("id") or service_result.get("data", {}).get("document", {}).get("id")
                logger.info(f"外部文档上传成功 for OA Doc ID: {doc.id}, External Doc ID: {external_doc_id}")
            else:
                upload_success = False
                error_detail_external = service_result.get("error", f"Unknown {provider.provider_type} service error")
                logger.error(f"外部文档上传失败 for OA Doc ID: {doc.id}. 原因: {error_detail_external}")
                logger.error(f"外部服务详细错误信息: {service_result}")

        else:
             # 服务实例没有 upload_document 方法，例如纯本地知识库
             logger.info(f"知识库 {kb.id} 的提供商类型 ({provider.provider_type}) 不支持外部上传，本地创建即视为成功。")
             upload_success = True # Local DB creation is sufficient

    except Exception as e:
        upload_success = False
        error_detail_external = f"调用知识库服务或处理上传时出错: {str(e)}"
        logger.error(f"调用知识库服务失败 for OA Doc ID: {doc.id}", exc_info=True)

    # 6. 根据外部服务上传结果更新 OA 文档状态和信息
    final_status = DocumentStatus.PROCESSING.value if upload_success else DocumentStatus.FAILED.value
    update_data = {"status": final_status}
    if external_doc_id:
        # 使用正确的字段名 external_id
        update_data["external_id"] = external_doc_id
    if not upload_success:
        # 可以在 metadata 中记录失败原因
        doc_metadata = doc.doc_metadata or {}
        doc_metadata["upload_error"] = error_detail_external
        update_data["doc_metadata"] = doc_metadata

    updated_doc = crud.knowledge_document.update(db, db_obj=doc, obj_in=update_data)
    logger.info(f"更新 OA DB 文档记录 ID: {doc.id} 状态为: {final_status}, external_id: {external_doc_id}")

    # 7. 记录审计日志 (可以在操作完成后记录最终状态)
    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id, kb_id=kb_id, doc_id=updated_doc.id,
        action="upload_document_completed", # 可以用不同的 action 区分
        details={
            "name": updated_doc.name,
            "final_status": final_status,
            "dify_doc_id": external_doc_id,
            "error": None if upload_success else error_detail_external
            # "file_size": file_size, # 需要从 file 或 service 获取
            # "content_type": file.content_type # 需要从 file 获取
        }
    )

    # 8. 如果上传失败，向前端抛出异常
    if not upload_success:
        raise HTTPException(
            status_code=502, # Bad Gateway 表示上游服务(Dify)出错
            detail=f"文件上传到外部知识库失败: {error_detail_external}"
        )

    # 在成功创建文档后，启动后台任务检查文档状态
    if updated_doc and updated_doc.external_id:
        background_tasks.add_task(
            check_document_status,
            db=db,
            doc_id=updated_doc.id,
            kb_external_id=kb.external_id,
            external_doc_id=updated_doc.external_id,
            provider=provider
        )

    # 9. 如果成功，返回更新后的文档信息
    # 需要确保返回的 DocumentResponse 包含 status 和可能的 external_document_id
    return updated_doc # 返回的是 SQLAlchemy 模型，FastAPI 会自动序列化


@router.get("/bases/{kb_id}/documents", response_model=List[DocumentResponse])
def get_documents(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    kb_id: int = Path(..., title="知识库ID"),
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    status: Optional[str] = None
) -> Any:
    """
    获取文档列表
    """
    # 获取知识库
    kb = crud.knowledge_base.get(db, kb_id=kb_id)
    if not kb:
        raise HTTPException(
            status_code=404,
            detail="Knowledge base not found"
        )

    # 检查权限
    if kb.owner_id != current_user.id and not kb.is_public:
        # 检查是否有权限
        permission = crud.knowledge_permission.get_by_user(
            db, kb_id=kb_id, user_id=current_user.id
        )
        if not permission:
            raise HTTPException(
                status_code=403,
                detail="Not enough permissions"
            )

    # 根据搜索条件获取文档
    if search:
        docs = crud.knowledge_document.search(
            db, kb_id=kb_id, search_term=search,
            skip=skip, limit=limit
        )
    else:
        docs = crud.knowledge_document.get_multi(
            db, kb_id=kb_id, skip=skip, limit=limit, status=status
        )

    # 确保所有文档都有有效的status值
    for doc in docs:
        if doc.status is None:
            # 如果状态为None，设置为处理中
            crud.knowledge_document.update(
                db,
                db_obj=doc,
                obj_in={"status": DocumentStatus.PROCESSING.value}
            )

    # 重新查询更新后的文档
    if search:
        docs = crud.knowledge_document.search(
            db, kb_id=kb_id, search_term=search,
            skip=skip, limit=limit
        )
    else:
        docs = crud.knowledge_document.get_multi(
            db, kb_id=kb_id, skip=skip, limit=limit, status=status
        )

    return docs


@router.get("/bases/{kb_id}/documents/{doc_id}", response_model=DocumentResponse)
def get_document(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    kb_id: int = Path(..., title="知识库ID"),
    doc_id: int = Path(..., title="文档ID")
) -> Any:
    """
    获取文档详情
    """
    # 获取知识库
    kb = crud.knowledge_base.get(db, kb_id=kb_id)
    if not kb:
        raise HTTPException(
            status_code=404,
            detail="Knowledge base not found"
        )

    # 检查权限
    if kb.owner_id != current_user.id and not kb.is_public:
        # 检查是否有权限
        permission = crud.knowledge_permission.get_by_user(
            db, kb_id=kb_id, user_id=current_user.id
        )
        if not permission:
            raise HTTPException(
                status_code=403,
                detail="Not enough permissions"
            )

    # 获取文档
    doc = crud.knowledge_document.get(db, doc_id=doc_id)
    if not doc or doc.knowledge_base_id != kb_id:
        raise HTTPException(
            status_code=404,
            detail="Document not found"
        )

    # 记录审计日志
    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id, kb_id=kb_id, doc_id=doc_id,
        action="view_document",
        details={"name": doc.name}
    )

    return doc


@router.put("/bases/{kb_id}/documents/{doc_id}", response_model=DocumentResponse)
def update_document(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    kb_id: int = Path(..., title="知识库ID"),
    doc_id: int = Path(..., title="文档ID"),
    doc_in: DocumentUpdate
) -> Any:
    """
    更新文档
    """
    # 获取知识库
    kb = crud.knowledge_base.get(db, kb_id=kb_id)
    if not kb:
        raise HTTPException(
            status_code=404,
            detail="Knowledge base not found"
        )

    # 检查权限
    if kb.owner_id != current_user.id:
        # 检查是否有写入权限
        permission = crud.knowledge_permission.get_by_user(
            db, kb_id=kb_id, user_id=current_user.id
        )
        if not permission or permission.permission_type not in [
            PermissionType.WRITE.value, PermissionType.ADMIN.value
        ]:
            raise HTTPException(
                status_code=403,
                detail="Not enough permissions"
            )

    # 获取文档
    doc = crud.knowledge_document.get(db, doc_id=doc_id)
    if not doc or doc.knowledge_base_id != kb_id:
        raise HTTPException(
            status_code=404,
            detail="Document not found"
        )

    # 更新文档
    doc = crud.knowledge_document.update(db, db_obj=doc, obj_in=doc_in)

    # 记录审计日志
    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id, kb_id=kb_id, doc_id=doc_id,
        action="update_document",
        details={"name": doc.name, "updated_fields": doc_in.dict(exclude_unset=True)}
    )

    return doc


@router.delete("/bases/{kb_id}/documents/{doc_id}", response_model=DocumentResponse)
async def delete_document(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    kb_id: int = Path(..., title="知识库ID"),
    doc_id: int = Path(..., title="文档ID")
) -> Any:
    """
    删除文档
    """
    # 获取知识库
    kb = crud.knowledge_base.get(db, kb_id=kb_id)
    if not kb:
        raise HTTPException(
            status_code=404,
            detail="Knowledge base not found"
        )

    # 检查权限
    if kb.owner_id != current_user.id:
        # 检查是否有写入权限
        permission = crud.knowledge_permission.get_by_user(
            db, kb_id=kb_id, user_id=current_user.id
        )
        if not permission or permission.permission_type not in [
            PermissionType.WRITE.value, PermissionType.ADMIN.value
        ]:
            raise HTTPException(
                status_code=403,
                detail="Not enough permissions"
            )

    # 获取文档
    doc = crud.knowledge_document.get(db, doc_id=doc_id)
    if not doc or doc.knowledge_base_id != kb_id:
        raise HTTPException(
            status_code=404,
            detail="Document not found"
        )

    # 记录审计日志
    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id, kb_id=kb_id, doc_id=doc_id,
        action="delete_document",
        details={"name": doc.name}
    )

    # 检查是否有外部ID
    if kb.external_id and doc.external_id:
        # 获取知识库提供商
        provider = crud.knowledge_provider.get(db, provider_id=kb.provider_id)
        if not provider:
            raise HTTPException(
                status_code=404,
                detail="Knowledge provider not found"
            )

        try:
            # 初始化对应的知识库服务
            knowledge_service = get_knowledge_service(provider)

            # 调用API删除远程文档
            result = await knowledge_service.delete_document(
                dataset_id=kb.external_id,
                document_id=doc.external_id
            )

            # 如果返回 404，说明文档在 Dify 中已经不存在，继续执行本地删除
            if result.get("status_code") == 404:
                logger.info(f"文档在 Dify 中已不存在，继续执行本地删除: {doc.external_id}")
            elif not result.get("success"):
                # 记录删除失败的日志
                error_msg = result.get("error", "Unknown error")
                crud.knowledge_audit_log.create_log(
                    db, user_id=current_user.id, kb_id=kb_id, doc_id=doc_id,
                    action="delete_document_error",
                    details={
                        "name": doc.name,
                        "error": error_msg,
                        "provider": provider.name
                    }
                )
                # 抛出异常，不继续删除本地文档
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to delete document in provider: {error_msg}"
                )
        except Exception as e:
            # 捕获异常并记录错误日志
            import traceback
            error_detail = traceback.format_exc()
            crud.knowledge_audit_log.create_log(
                db, user_id=current_user.id, kb_id=kb_id, doc_id=doc_id,
                action="delete_document_error",
                details={"name": doc.name, "error": str(e), "error_detail": error_detail}
            )
            # 抛出异常，不继续删除本地文档
            raise HTTPException(
                status_code=500,
                detail=f"Error deleting document in provider: {str(e)}"
            )

    # 删除本地文档
    doc = crud.knowledge_document.delete(db, doc_id=doc_id)

    return doc


@router.post("/bases/{kb_id}/documents/search", response_model=List[SearchResult])
def search_documents(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    kb_id: int = Path(..., title="知识库ID"),
    query: SearchQuery
) -> Any:
    """
    搜索文档
    """
    # 获取知识库
    kb = crud.knowledge_base.get(db, kb_id=kb_id)
    if not kb:
        raise HTTPException(
            status_code=404,
            detail="Knowledge base not found"
        )

    # 检查权限
    if kb.owner_id != current_user.id and not kb.is_public:
        # 检查是否有权限
        permission = crud.knowledge_permission.get_by_user(
            db, kb_id=kb_id, user_id=current_user.id
        )
        if not permission:
            raise HTTPException(
                status_code=403,
                detail="Not enough permissions"
            )

    # 搜索文档
    docs = crud.knowledge_document.search(
        db, kb_id=kb_id, search_term=query.query,
        skip=0, limit=query.limit or 10
    )

    # 记录审计日志
    crud.knowledge_audit_log.create_log(
        db, user_id=current_user.id, kb_id=kb_id,
        action="search_documents",
        details={"query": query.query, "result_count": len(docs)}
    )

    # 构建搜索结果
    results = []
    for doc in docs:
        # 计算相关性分数（简单实现，实际应使用向量搜索或全文搜索引擎）
        relevance_score = 0.8  # 示例分数

        # 提取片段
        snippet = doc.content_preview[:200] if doc.content_preview else None

        results.append(
            SearchResult(
                document_id=doc.id,
                name=doc.name,
                description=doc.description,
                document_type=doc.document_type,
                content_preview=doc.content_preview,
                relevance_score=relevance_score,
                snippet=snippet
            )
        )

    return results
