"""
流程API V1版本
这个模块将非v1版本的流程API转发到v1路径
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Any, List
import logging

from app.api import dependencies as deps
from app.models.user import User
from app.models.process import Process, ProcessTemplate, ProcessApproval, ProcessStatus
from app.schemas.process import (
    ProcessCreate,
    ProcessResponse,
    ProcessTemplate as ProcessTemplateSchema,
    ProcessTemplateCreate,
    ProcessTemplateUpdate
)
from app.core.config import settings

# 初始化日志
logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/templates", response_model=List[ProcessTemplateSchema])
def get_process_templates_v1(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """V1版本 - 获取流程模板列表"""
    logger.info(f"V1 API: 获取流程模板列表, 用户ID: {current_user.id}")

    templates = (
        db.query(ProcessTemplate)
        .filter(ProcessTemplate.is_active == True)
        .offset(skip)
        .limit(limit)
        .all()
    )

    # 处理steps字段，确保它是字典对象而不是JSON字符串
    import json
    for template in templates:
        if isinstance(template.steps, str):
            try:
                template.steps = json.loads(template.steps)
            except json.JSONDecodeError:
                # 如果JSON解析失败，保持原样
                pass

    return templates


@router.get("/my-processes", response_model=List[ProcessResponse])
def get_my_processes_v1(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """V1版本 - 获取我的流程列表"""
    # 获取用户ID
    user_id = current_user.id
    logger.info(f"V1 API: 获取我的流程列表, 用户ID: {user_id}")

    processes = (
        db.query(Process)
        .filter(Process.created_by == user_id)
        .offset(skip)
        .limit(limit)
        .all()
    )

    # 转换时区
    for process in processes:
        process.created_at = process.created_at.astimezone(settings.TZ)
        if process.updated_at is not None:
            process.updated_at = process.updated_at.astimezone(settings.TZ)
        else:
            process.updated_at = process.created_at

    logger.info(f"V1 API: 成功获取我的流程列表, 数量: {len(processes)}")
    return processes


@router.post("", response_model=ProcessResponse)
def create_process_v1(
    *,
    db: Session = Depends(deps.get_db),
    process_in: ProcessCreate,
    current_user: User = Depends(deps.get_current_user),
) -> Any:
    """V1版本 - 创建流程"""
    # 获取用户ID
    user_id = current_user.id
    logger.info(f"V1 API: 创建流程, 模板ID: {process_in.template_id}, 用户ID: {user_id}")

    # 检查模板是否存在
    template = db.query(ProcessTemplate).filter(
        ProcessTemplate.id == process_in.template_id,
        ProcessTemplate.is_active == True
    ).first()

    if not template:
        logger.error(f"V1 API: 流程模板不存在, ID: {process_in.template_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="流程模板不存在"
        )

    # 开发环境下，如果用户ID为空，使用默认值
    if not user_id:
        logger.warning("V1 API: 无法获取用户ID，使用默认值1（仅在开发/测试环境下）")
        user_id = 1

    # 创建流程
    process = Process(
        title=process_in.title,
        description=process_in.description,
        template_id=process_in.template_id,
        created_by=user_id,
        current_step=0,  # 初始步骤
        status=ProcessStatus.PENDING,  # 直接设置为待审批状态
        data=process_in.data
    )

    db.add(process)
    db.commit()
    db.refresh(process)
    logger.info(f"V1 API: 流程创建成功, ID: {process.id}, 状态: {process.status}")

    # 更新模板使用次数
    template.usage_count += 1
    db.commit()

    return process


@router.get("/{process_id}", response_model=ProcessResponse)
def get_process_v1(
    *,
    db: Session = Depends(deps.get_db),
    process_id: int,
    current_user: User = Depends(deps.get_current_user),
) -> Any:
    """V1版本 - 获取流程详情"""
    # 获取用户ID
    user_id = current_user.id
    logger.info(f"V1 API: 获取流程详情, 流程ID: {process_id}, 用户ID: {user_id}")

    process = db.query(Process).filter(Process.id == process_id).first()

    if not process:
        logger.error(f"V1 API: 流程不存在, ID: {process_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="流程不存在"
        )

    # 检查权限：只有流程创建者或审批人可以查看
    is_approver = db.query(ProcessApproval).filter(
        ProcessApproval.process_id == process_id,
        ProcessApproval.approver_id == user_id
    ).first() is not None

    if process.created_by != user_id and not is_approver:
        logger.warning(f"V1 API: 用户无权限查看流程, 用户ID: {user_id}, 流程ID: {process_id}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    # 转换时区为上海时区
    process.created_at = process.created_at.astimezone(settings.TZ)
    if process.updated_at is not None:
        process.updated_at = process.updated_at.astimezone(settings.TZ)
    else:
        # 如果updated_at为None，设置为created_at的值
        process.updated_at = process.created_at

    # 转换为响应模型
    response = ProcessResponse.from_orm(process)

    # 格式化数据，转换字段名为中文
    response.data = response.format_data()

    logger.info(f"V1 API: 成功获取流程详情, ID: {process_id}")
    return response
