from typing import Any

from sqlalchemy.ext.declarative import as_declarative, declared_attr


@as_declarative()
class Base:
    """
    SQLAlchemy 声明性基类
    """
    id: Any
    __name__: str

    # 根据类名自动生成表名
    @declared_attr
    def __tablename__(cls) -> str:
        return cls.__name__.lower()

    # 自动设置 schema
    @declared_attr
    def __table_args__(cls):
        from app.core.config import settings
        return {'schema': settings.POSTGRES_SCHEMA}
