from sqlalchemy.orm import Session
import json
from datetime import datetime

from app.core.security import get_password_hash
from app.models.user import User
from app.models.process import ProcessTemplate
from app.models.knowledge import KnowledgeBase, KnowledgeProvider
from app.models.report import ReportTemplate


def init_db(db: Session) -> None:
    """初始化数据库，添加一些基础数据"""
    # 创建管理员用户
    admin = db.query(User).filter(User.username == "admin").first()
    if not admin:
        admin = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=get_password_hash("admin"),
            is_active=True,
            is_superuser=True,
            full_name="管理员"
        )
        db.add(admin)
        db.flush()  # 确保用户ID可用

    # 创建知识库提供商
    if db.query(KnowledgeProvider).count() == 0:
        dify_provider = KnowledgeProvider(
            name="Dify",
            provider_type="api",
            base_url="",
            api_key="",
            description="",
            is_active=True
        )
        db.add(dify_provider)

    # 创建流程模板
    if db.query(ProcessTemplate).count() == 0:
        templates = [
            ProcessTemplate(
                id=1,
                name="差旅报销申请",
                description="用于员工出差后的费用报销，包括交通、住宿、餐饮等费用",
                type="报销申请",
                department="全公司",
                steps=json.dumps({
                    "approvalFlow": [
                        {
                            "id": "node1",
                            "type": "role",
                            "approver": "部门经理",
                            "description": "部门初审"
                        },
                        {
                            "id": "node2",
                            "type": "role",
                            "approver": "财务经理",
                            "description": "财务审核",
                            "conditions": [
                                {
                                    "field": "totalAmount",
                                    "operator": "greater",
                                    "value": 5000
                                }
                            ]
                        }
                    ]
                }),
                form_schema=json.dumps({
                    "formFields": [
                        {
                            "id": "field1",
                            "name": "tripPurpose",
                            "label": "出差目的",
                            "type": "text",
                            "required": True
                        },
                        {
                            "id": "field2",
                            "name": "destination",
                            "label": "目的地",
                            "type": "text",
                            "required": True
                        },
                        {
                            "id": "field3",
                            "name": "dateRange",
                            "label": "出差日期",
                            "type": "dateRange",
                            "required": True
                        },
                        {
                            "id": "field4",
                            "name": "expenseItems",
                            "label": "费用明细",
                            "type": "table",
                            "required": True
                        },
                        {
                            "id": "field5",
                            "name": "totalAmount",
                            "label": "总金额",
                            "type": "number",
                            "required": True
                        },
                        {
                            "id": "field6",
                            "name": "attachments",
                            "label": "附件",
                            "type": "upload",
                            "required": True
                        },
                        {
                            "id": "field7",
                            "name": "notes",
                            "label": "备注",
                            "type": "textarea",
                            "required": False
                        }
                    ]
                }),
                usage_count=28,
                version="1.0",
                status="active",
                created_by=1,
                is_active=True
            ),
            ProcessTemplate(
                id=2,
                name="年假申请",
                description="用于员工申请年假，需提前3天提交申请",
                type="休假申请",
                department="全公司",
                steps=json.dumps({
                    "approvalFlow": [
                        {
                            "id": "node1",
                            "type": "role",
                            "approver": "部门经理",
                            "description": "部门审批"
                        },
                        {
                            "id": "node2",
                            "type": "role",
                            "approver": "人事主管",
                            "description": "人事审核",
                            "conditions": [
                                {
                                    "field": "days",
                                    "operator": "greater",
                                    "value": 3
                                }
                            ]
                        }
                    ]
                }),
                form_schema=json.dumps({
                    "formFields": [
                        {
                            "id": "field1",
                            "name": "leaveType",
                            "label": "休假类型",
                            "type": "select",
                            "required": True,
                            "options": [
                                {"label": "年假", "value": "annual"},
                                {"label": "病假", "value": "sick"},
                                {"label": "事假", "value": "personal"}
                            ]
                        },
                        {
                            "id": "field2",
                            "name": "dateRange",
                            "label": "休假日期",
                            "type": "dateRange",
                            "required": True
                        },
                        {
                            "id": "field3",
                            "name": "days",
                            "label": "休假天数",
                            "type": "number",
                            "required": True
                        },
                        {
                            "id": "field4",
                            "name": "reason",
                            "label": "休假事由",
                            "type": "textarea",
                            "required": True
                        },
                        {
                            "id": "field5",
                            "name": "contact",
                            "label": "紧急联系方式",
                            "type": "text",
                            "required": True
                        },
                        {
                            "id": "field6",
                            "name": "handover",
                            "label": "工作交接人",
                            "type": "select",
                            "required": True
                        }
                    ]
                }),
                usage_count=45,
                version="1.2",
                status="active",
                created_by=1,
                is_active=True
            ),
            ProcessTemplate(
                id=3,
                name="办公用品采购申请",
                description="用于申请采购办公用品，包括文具、设备等",
                type="采购申请",
                department="行政部",
                steps=json.dumps({
                    "approvalFlow": [
                        {
                            "id": "node1",
                            "type": "role",
                            "approver": "行政经理",
                            "description": "行政审核"
                        },
                        {
                            "id": "node2",
                            "type": "role",
                            "approver": "财务经理",
                            "description": "财务审批",
                            "conditions": [
                                {
                                    "field": "totalAmount",
                                    "operator": "greater",
                                    "value": 1000
                                }
                            ]
                        }
                    ]
                }),
                form_schema=json.dumps({
                    "formFields": [
                        {
                            "id": "field1",
                            "name": "itemList",
                            "label": "采购清单",
                            "type": "table",
                            "required": True
                        },
                        {
                            "id": "field2",
                            "name": "totalAmount",
                            "label": "总金额",
                            "type": "number",
                            "required": True
                        },
                        {
                            "id": "field3",
                            "name": "purpose",
                            "label": "用途说明",
                            "type": "textarea",
                            "required": True
                        },
                        {
                            "id": "field4",
                            "name": "expectedDelivery",
                            "label": "期望交付日期",
                            "type": "date",
                            "required": True
                        },
                        {
                            "id": "field5",
                            "name": "supplier",
                            "label": "建议供应商",
                            "type": "text",
                            "required": False
                        }
                    ]
                }),
                usage_count=17,
                version="1.0",
                status="active",
                created_by=1,
                is_active=True
            ),
            ProcessTemplate(
                id=4,
                name="项目立项申请",
                description="用于新项目的立项申请，包括项目计划、资源需求等",
                type="项目申请",
                department="技术部",
                steps=json.dumps({
                    "approvalFlow": [
                        {
                            "id": "node1",
                            "type": "role",
                            "approver": "技术总裁",
                            "description": "技术可行性评估"
                        },
                        {
                            "id": "node2",
                            "type": "role",
                            "approver": "产品总裁",
                            "description": "产品价值评估"
                        },
                        {
                            "id": "node3",
                            "type": "role",
                            "approver": "总经理",
                            "description": "最终审批",
                            "conditions": [
                                {
                                    "field": "budget",
                                    "operator": "greater",
                                    "value": 100000
                                }
                            ]
                        }
                    ]
                }),
                form_schema=json.dumps({
                    "formFields": [
                        {
                            "id": "field1",
                            "name": "projectName",
                            "label": "项目名称",
                            "type": "text",
                            "required": True
                        },
                        {
                            "id": "field2",
                            "name": "projectType",
                            "label": "项目类型",
                            "type": "select",
                            "required": True,
                            "options": [
                                {"label": "研发项目", "value": "rd"},
                                {"label": "基础设施", "value": "infra"},
                                {"label": "业务系统", "value": "business"}
                            ]
                        },
                        {
                            "id": "field3",
                            "name": "objectives",
                            "label": "项目目标",
                            "type": "textarea",
                            "required": True
                        },
                        {
                            "id": "field4",
                            "name": "schedule",
                            "label": "项目计划",
                            "type": "table",
                            "required": True
                        },
                        {
                            "id": "field5",
                            "name": "resources",
                            "label": "资源需求",
                            "type": "table",
                            "required": True
                        },
                        {
                            "id": "field6",
                            "name": "budget",
                            "label": "预算",
                            "type": "number",
                            "required": True
                        },
                        {
                            "id": "field7",
                            "name": "risks",
                            "label": "风险评估",
                            "type": "textarea",
                            "required": True
                        },
                        {
                            "id": "field8",
                            "name": "attachments",
                            "label": "附件",
                            "type": "upload",
                            "required": False
                        }
                    ]
                }),
                usage_count=8,
                version="1.1",
                status="active",
                created_by=1,
                is_active=True
            ),
            ProcessTemplate(
                id=5,
                name="加班申请",
                description="用于员工申请加班，包括加班时间、原因等",
                type="工时申请",
                department="全公司",
                steps=json.dumps({
                    "approvalFlow": [
                        {
                            "id": "node1",
                            "type": "role",
                            "approver": "部门经理",
                            "description": "加班审批",
                            "conditions": [
                                {
                                    "field": "hours",
                                    "operator": "greater",
                                    "value": 4
                                }
                            ]
                        }
                    ]
                }),
                form_schema=json.dumps({
                    "formFields": [
                        {
                            "id": "field1",
                            "name": "overtimeDate",
                            "label": "加班日期",
                            "type": "date",
                            "required": True
                        },
                        {
                            "id": "field2",
                            "name": "timeRange",
                            "label": "加班时间段",
                            "type": "date",
                            "required": True
                        },
                        {
                            "id": "field3",
                            "name": "hours",
                            "label": "加班小时数",
                            "type": "number",
                            "required": True
                        },
                        {
                            "id": "field4",
                            "name": "reason",
                            "label": "加班原因",
                            "type": "textarea",
                            "required": True
                        },
                        {
                            "id": "field5",
                            "name": "compensationType",
                            "label": "补偿方式",
                            "type": "radio",
                            "required": True,
                            "options": [
                                {"label": "调休", "value": "leave"},
                                {"label": "加班费", "value": "payment"}
                            ]
                        }
                    ]
                }),
                usage_count=15,
                version="1.0",
                status="active",
                created_by=1,
                is_active=True
            )
        ]
        db.add_all(templates)

    # 创建一些基础知识库
    if db.query(KnowledgeBase).count() == 0:
        knowledge_bases = [
            KnowledgeBase(
                name="公司规章制度",
                description="包含公司各项规章制度和员工手册",
                owner_id=1
            ),
            KnowledgeBase(
                name="产品知识库",
                description="包含公司产品相关的知识和文档",
                owner_id=1
            )
        ]
        db.add_all(knowledge_bases)

    # 创建一些报告模板
    if db.query(ReportTemplate).count() == 0:
        report_templates = [
            ReportTemplate(
                name="周报模板",
                description="标准周报模板，包含本周工作总结和下周计划",
                template_content=json.dumps({
                    "sections": [
                        {"title": "本周工作总结", "type": "text"},
                        {"title": "工作完成情况", "type": "table"},
                        {"title": "遇到的问题", "type": "text"},
                        {"title": "下周工作计划", "type": "text"}
                    ]
                }),
                created_by=1
            ),
            ReportTemplate(
                name="月度销售报告",
                description="销售部门月度报告模板",
                template_content=json.dumps({
                    "sections": [
                        {"title": "销售概览", "type": "text"},
                        {"title": "销售数据", "type": "chart"},
                        {"title": "客户分析", "type": "table"},
                        {"title": "下月销售目标", "type": "text"}
                    ]
                }),
                created_by=1
            )
        ]
        db.add_all(report_templates)

    db.commit()
