#!/usr/bin/env python3
"""
测试 init_db 函数
"""
import os
import sys

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.db.session import engine, get_db
from app.db.init_db import init_db
from app.models.user import User
from app.models.process import ProcessTemplate
from app.models.knowledge import KnowledgeProvider, KnowledgeBase
from app.models.report import ReportTemplate

def test_init_db():
    """测试数据库初始化"""
    print(f"当前配置的 POSTGRES_SCHEMA: {settings.POSTGRES_SCHEMA}")
    print(f"数据库连接 URL: {settings.DATABASE_URL}")
    print()
    
    # 获取数据库会话
    db = next(get_db())
    
    try:
        # 清空现有数据（仅用于测试）
        print("清空现有数据...")
        db.query(ProcessTemplate).delete()
        db.query(KnowledgeBase).delete()
        db.query(KnowledgeProvider).delete()
        db.query(ReportTemplate).delete()
        db.query(User).delete()
        db.commit()
        
        # 运行初始化
        print("运行数据库初始化...")
        init_db(db)
        
        # 验证数据
        print("\n验证初始化结果:")
        print("-" * 50)
        
        # 检查用户
        user_count = db.query(User).count()
        print(f"用户数量: {user_count}")
        if user_count > 0:
            admin = db.query(User).filter(User.username == "admin").first()
            if admin:
                print(f"✅ 管理员用户创建成功: {admin.username} ({admin.full_name})")
        
        # 检查知识库提供商
        provider_count = db.query(KnowledgeProvider).count()
        print(f"知识库提供商数量: {provider_count}")
        if provider_count > 0:
            dify_provider = db.query(KnowledgeProvider).filter(KnowledgeProvider.name == "Dify").first()
            if dify_provider:
                print(f"✅ Dify 提供商创建成功: {dify_provider.name} ({dify_provider.provider_type})")
        
        # 检查流程模板
        template_count = db.query(ProcessTemplate).count()
        print(f"流程模板数量: {template_count}")
        if template_count > 0:
            templates = db.query(ProcessTemplate).all()
            for template in templates:
                print(f"✅ 流程模板: {template.name} ({template.type}) - 使用次数: {template.usage_count}")
        
        # 检查知识库
        kb_count = db.query(KnowledgeBase).count()
        print(f"知识库数量: {kb_count}")
        
        # 检查报告模板
        report_count = db.query(ReportTemplate).count()
        print(f"报告模板数量: {report_count}")
        
        print("\n" + "=" * 50)
        print("数据库初始化测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("=" * 60)
    print("测试数据库初始化函数")
    print("=" * 60)
    
    test_init_db()
