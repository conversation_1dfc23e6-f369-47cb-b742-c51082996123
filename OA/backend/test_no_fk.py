#!/usr/bin/env python3
"""
测试去掉外键约束后的模型定义
"""
import os
import sys

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.db.base import Base
from app.db.session import engine

def test_simple_models():
    """测试简单模型定义（不涉及关系）"""
    print(f"当前配置的 POSTGRES_SCHEMA: {settings.POSTGRES_SCHEMA}")
    print()
    
    # 只导入基础模型，不涉及复杂关系
    from app.models.user import User
    from app.models.dify_app import DifyApp
    
    print("基础模型导入成功:")
    print(f"- User 表: {User.__table__.fullname}")
    print(f"- DifyApp 表: {DifyApp.__table__.fullname}")
    
    return True

def test_table_creation():
    """测试表创建"""
    try:
        print("\n开始创建基础表...")
        
        # 只创建基础表
        from app.models.user import User
        from app.models.dify_app import DifyApp
        
        # 创建这些表
        tables_to_create = [User.__table__, DifyApp.__table__]
        
        for table in tables_to_create:
            print(f"创建表: {table.fullname}")
            table.create(engine, checkfirst=True)
        
        print("基础表创建成功!")
        return True
        
    except Exception as e:
        print(f"表创建失败: {e}")
        return False

def test_database_operations():
    """测试基本数据库操作"""
    try:
        from sqlalchemy.orm import sessionmaker
        from app.models.user import User
        
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # 测试查询（不会实际查询数据，只是测试SQL生成）
        print("\n测试基本查询...")
        query = session.query(User).filter(User.id == 1)
        print(f"生成的SQL: {query}")
        
        session.close()
        print("数据库操作测试成功!")
        return True
        
    except Exception as e:
        print(f"数据库操作测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("测试去掉外键约束后的模型")
    print("=" * 60)
    
    # 测试模型导入
    if test_simple_models():
        print("\n" + "=" * 60)
        
        # 测试表创建
        if test_table_creation():
            print("\n" + "=" * 60)
            
            # 测试数据库操作
            test_database_operations()
    
    print("\n" + "=" * 60)
    print("测试完成!")
