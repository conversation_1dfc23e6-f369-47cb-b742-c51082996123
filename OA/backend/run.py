import uvicorn
import os
import sys
from sqlalchemy import create_engine, inspect
from sqlalchemy.orm import sessionmaker
from fastapi import FastAPI
import psycopg2
import logging

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.db.init_db import init_db
from app.db.base import Base
from app.main import app as base_app

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_tables_exist(engine):
    """检查所有必要的表是否都存在"""
    inspector = inspect(engine)
    existing_tables = inspector.get_table_names()
    required_tables = Base.metadata.tables.keys()

    missing_tables = [table for table in required_tables if table not in existing_tables]
    if missing_tables:
        logger.info(f"以下表不存在，需要创建: {', '.join(missing_tables)}")
        return False
    return True

def init_database():
    """初始化数据库和基础数据"""
    try:
        # 1. 创建PostgreSQL数据库
        conn = psycopg2.connect(
            host=settings.POSTGRES_SERVER,
            port=settings.POSTGRES_PORT,
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD,
            database="postgres"
        )
        conn.autocommit = True
        cursor = conn.cursor()

        # 检查数据库是否存在
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (settings.POSTGRES_DB,))
        if not cursor.fetchone():
            # 创建数据库
            cursor.execute(f"CREATE DATABASE {settings.POSTGRES_DB}")
            logger.info(f"数据库 {settings.POSTGRES_DB} 创建成功")
        else:
            logger.info(f"数据库 {settings.POSTGRES_DB} 已存在")

        cursor.close()
        conn.close()

        # 2. 创建数据库表
        engine = create_engine(settings.DATABASE_URL)

        # 检查表是否存在
        if not check_tables_exist(engine):
            logger.info("开始创建数据库表...")
            Base.metadata.create_all(bind=engine)
            logger.info("数据库表创建完成")
        else:
            logger.info("所有必要的表已存在，跳过创建")

        # 3. 初始化基础数据
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()

        try:
            # 检查是否有用户数据
            from app.models.user import User
            if not db.query(User).first():
                logger.info("数据库为空，初始化基础数据...")
                init_db(db)
                logger.info("基础数据初始化完成")
            else:
                logger.info("数据库已有数据，跳过初始化")
        finally:
            db.close()

    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        raise

def mount_app():
    """创建主应用并挂载到/hitox路径下"""
    # 创建主FastAPI应用
    main_app = FastAPI()

    # 挂载OA应用到/hitox路径
    main_app.mount(settings.API_ROOT_PATH, base_app)

    return main_app

# 创建并使用挂载好的应用
app = mount_app()

if __name__ == "__main__":
    # 初始化数据库
    init_database()

    # 启动应用
    uvicorn.run(
        "run:app",
        host="0.0.0.0",
        port=8001,
        reload=True
    )
