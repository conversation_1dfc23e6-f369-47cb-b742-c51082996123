# 数据库 Schema 配置和外键约束移除总结

## 概述

本次修改主要解决了两个问题：
1. **PostgreSQL Schema 配置**: 使 `POSTGRES_SCHEMA` 环境变量能够正确应用到所有数据库表
2. **外键约束移除**: 为了支持未来的分库分表和微服务架构，移除了所有数据库外键约束

## 主要修改

### 1. Schema 配置 ✅

#### 修改的文件：
- `app/db/base_class.py`: 添加了自动 schema 配置
- `app/models/knowledge.py`: 更新了关联表的 schema 配置

#### 实现方式：
```python
# 在 Base 类中添加自动 schema 配置
@declared_attr
def __table_args__(cls):
    from app.core.config import settings
    return {'schema': settings.POSTGRES_SCHEMA}
```

#### 配置方法：
```bash
# 通过环境变量设置
export DB_SCHEMA=oa_production

# 或在 .env 文件中设置
DB_SCHEMA=oa_development

# 在 Kubernetes 中设置
env:
  - name: DB_SCHEMA
    value: "oa_production"
```

### 2. 外键约束移除 ✅

#### 移除原因：
- **分库分表支持**: 外键约束会阻止跨库的数据分布
- **微服务架构**: 不同服务可以独立管理自己的数据库
- **性能提升**: 减少数据库约束检查的开销
- **部署灵活性**: 表创建顺序不再重要
- **数据迁移简化**: 更容易进行数据迁移和重构

#### 修改的模型文件：
- `app/models/knowledge.py`: 知识库相关模型
- `app/models/process.py`: 流程相关模型
- `app/models/chat.py`: 聊天相关模型
- `app/models/file.py`: 文件相关模型
- `app/models/report.py`: 报告相关模型
- `app/models/user.py`: 用户模型
- `app/models/user_dify_app_binding.py`: 用户应用绑定模型

#### 修改方式：
1. **移除 ForeignKey 导入**
2. **将外键字段改为普通整数字段**，添加注释标明为"逻辑外键"
3. **暂时注释掉所有 relationship 定义**，避免连接配置问题

#### 示例修改：
```python
# 修改前
user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
user = relationship("User", back_populates="items")

# 修改后
user_id = Column(Integer, nullable=True, comment="用户ID（逻辑外键）")
# user = relationship("User", back_populates="items")  # 暂时移除
```

### 3. 数据完整性保证

虽然移除了数据库级别的外键约束，但数据完整性通过以下方式保证：

#### 应用层约束：
- 在业务逻辑中验证关联数据的存在性
- 使用事务确保数据一致性
- 实现软删除而不是硬删除

#### 示例代码：
```python
# 在创建关联数据时验证
def create_process(db: Session, process_data: dict, user_id: int):
    # 验证用户存在
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise ValueError("用户不存在")
    
    # 验证模板存在
    if process_data.get("template_id"):
        template = db.query(ProcessTemplate).filter(
            ProcessTemplate.id == process_data["template_id"]
        ).first()
        if not template:
            raise ValueError("模板不存在")
    
    # 创建流程
    process = Process(**process_data, created_by=user_id)
    db.add(process)
    db.commit()
    return process
```

## 测试结果

### Schema 配置测试 ✅
- 所有表都正确使用了配置的 schema
- 可以通过环境变量动态切换 schema
- 支持多环境部署（开发、测试、生产）

### 应用启动测试 ✅
- 应用程序可以正常启动
- 数据库表创建成功
- 所有 API 路由正常加载

### 数据库连接测试 ✅
- 数据库连接正常
- Schema 自动创建（如果不存在）
- 表结构正确生成

## 使用场景

### 1. 多环境部署
```bash
# 开发环境
DB_SCHEMA=oa_development

# 测试环境  
DB_SCHEMA=oa_test

# 生产环境
DB_SCHEMA=oa_production
```

### 2. 多租户架构
```bash
# 租户1
DB_SCHEMA=tenant_001

# 租户2
DB_SCHEMA=tenant_002
```

### 3. 微服务拆分
每个微服务可以使用独立的 schema，便于后续拆分到独立的数据库。

## 注意事项

### 1. 关系查询
由于暂时移除了 SQLAlchemy 的 relationship 定义，需要手动进行关联查询：

```python
# 获取用户的所有流程
user_processes = db.query(Process).filter(Process.created_by == user_id).all()

# 获取流程的创建者
process = db.query(Process).filter(Process.id == process_id).first()
creator = db.query(User).filter(User.id == process.created_by).first()
```

### 2. 数据完整性
需要在应用层确保数据完整性，包括：
- 创建关联数据前验证父记录存在
- 删除父记录前处理子记录
- 使用事务确保操作的原子性

### 3. 性能考虑
- 为逻辑外键字段添加索引
- 合理使用查询优化
- 考虑使用缓存减少数据库查询

## 后续工作

### 1. 关系定义恢复（可选）
如果需要使用 SQLAlchemy 的 relationship 功能，可以添加 `primaryjoin` 参数：

```python
# 示例：恢复关系定义
user = relationship("User", 
                   primaryjoin="Process.created_by == foreign(User.id)")
```

### 2. 数据迁移工具
开发数据迁移工具，支持：
- Schema 之间的数据迁移
- 分库分表的数据分布
- 数据一致性检查

### 3. 监控和告警
添加数据完整性监控：
- 孤儿记录检测
- 数据一致性检查
- 性能监控

## 总结

本次修改成功实现了：
1. ✅ PostgreSQL Schema 的动态配置
2. ✅ 外键约束的完全移除
3. ✅ 应用程序的正常启动
4. ✅ 为分库分表和微服务架构做好准备

这些修改为系统的可扩展性和灵活性奠定了基础，支持未来的大规模部署和架构演进。
