#!/usr/bin/env python3
"""
测试 schema 配置是否正确应用
"""
import os
import sys

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.db.base import Base
from app.db.session import engine

# 导入所有模型以确保它们被注册
from app.models.user import User
from app.models.process import Process, ProcessTemplate, ProcessApproval
from app.models.knowledge import KnowledgeBase, KnowledgeProvider, KnowledgeTag, KnowledgeDocument, KnowledgePermission, KnowledgeAuditLog
from app.models.chat import Contact, UserContact, Message, AIConversation
from app.models.report import ReportTemplate, Report
from app.models.file import File
from app.models.dify_app import DifyApp
from app.models.user_dify_app_binding import UserDifyAppBinding

def test_schema_configuration():
    """测试 schema 配置"""
    print(f"当前配置的 POSTGRES_SCHEMA: {settings.POSTGRES_SCHEMA}")
    print(f"数据库连接 URL: {settings.DATABASE_URL}")
    print()

    print("检查所有表的 schema 配置:")
    print("-" * 50)

    # 检查所有表的 schema 配置
    for table_name, table in Base.metadata.tables.items():
        schema = table.schema
        print(f"表名: {table_name:<25} Schema: {schema}")

    print()
    print("检查外键约束:")
    print("-" * 50)

    # 检查外键约束
    for table_name, table in Base.metadata.tables.items():
        for fk in table.foreign_keys:
            print(f"表: {table_name:<20} 外键: {fk.parent.name:<20} -> {fk.target_fullname}")

def test_database_connection():
    """测试数据库连接"""
    try:
        print("测试数据库连接...")
        from sqlalchemy import text
        with engine.connect() as conn:
            result = conn.execute(text("SELECT current_schema()"))
            current_schema = result.fetchone()[0]
            print(f"当前数据库 schema: {current_schema}")

            # 检查配置的 schema 是否存在
            result = conn.execute(text(f"SELECT schema_name FROM information_schema.schemata WHERE schema_name = '{settings.POSTGRES_SCHEMA}'"))
            schema_exists = result.fetchone()

            if schema_exists:
                print(f"Schema '{settings.POSTGRES_SCHEMA}' 存在")
            else:
                print(f"Schema '{settings.POSTGRES_SCHEMA}' 不存在，需要创建")

        print("数据库连接测试成功!")
        return True
    except Exception as e:
        print(f"数据库连接测试失败: {e}")
        return False

def create_schema_if_not_exists():
    """如果 schema 不存在则创建"""
    try:
        from sqlalchemy import text
        with engine.connect() as conn:
            # 检查 schema 是否存在
            result = conn.execute(text(f"SELECT schema_name FROM information_schema.schemata WHERE schema_name = '{settings.POSTGRES_SCHEMA}'"))
            schema_exists = result.fetchone()

            if not schema_exists:
                print(f"创建 schema: {settings.POSTGRES_SCHEMA}")
                conn.execute(text(f"CREATE SCHEMA IF NOT EXISTS {settings.POSTGRES_SCHEMA}"))
                conn.commit()
                print(f"Schema '{settings.POSTGRES_SCHEMA}' 创建成功")
            else:
                print(f"Schema '{settings.POSTGRES_SCHEMA}' 已存在")

    except Exception as e:
        print(f"创建 schema 失败: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("测试 PostgreSQL Schema 配置")
    print("=" * 60)

    # 测试配置
    test_schema_configuration()

    print()
    print("=" * 60)

    # 测试数据库连接
    if test_database_connection():
        print()
        print("=" * 60)

        # 创建 schema（如果需要）
        create_schema_if_not_exists()

        print()
        print("测试完成!")
    else:
        print("数据库连接失败，请检查配置")
