#!/usr/bin/env python3
"""
测试知识库关系修复
"""
import os
import sys

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_knowledge_imports():
    """测试知识库相关模块导入"""
    print("测试知识库相关模块导入...")
    
    try:
        from app.models.knowledge import KnowledgeBase, KnowledgeProvider
        print("✅ 知识库模型导入成功")
        
        from app.api.v1.endpoints.knowledge import router
        print("✅ 知识库 API 端点导入成功")
        
        from app.crud.knowledge import knowledge_base, knowledge_provider
        print("✅ 知识库 CRUD 操作导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_knowledge_models():
    """测试知识库模型"""
    print("\n测试知识库模型...")
    
    try:
        from app.models.knowledge import KnowledgeBase, KnowledgeProvider
        
        # 创建提供商实例
        provider = KnowledgeProvider(
            name="Test Provider",
            provider_type="api",
            base_url="http://test.com",
            api_key="test_key",
            description="Test provider",
            is_active=True
        )
        print(f"✅ 提供商模型创建成功: {provider.name}")
        
        # 创建知识库实例
        kb = KnowledgeBase(
            name="Test KB",
            description="Test knowledge base",
            provider_id=1,  # 使用逻辑外键
            owner_id=1,
            is_public=False
        )
        print(f"✅ 知识库模型创建成功: {kb.name}")
        
        # 测试访问不存在的关系属性（应该会失败）
        try:
            provider_rel = kb.provider
            print(f"❌ 意外成功访问了 provider 关系: {provider_rel}")
        except AttributeError as e:
            print(f"✅ 正确抛出 AttributeError: {e}")
        
        return True
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_operations():
    """测试数据库操作"""
    print("\n测试数据库操作...")
    
    try:
        from app.db.session import get_db
        from app.models.knowledge import KnowledgeBase, KnowledgeProvider
        
        # 获取数据库会话
        db = next(get_db())
        
        try:
            # 测试查询提供商
            provider_count = db.query(KnowledgeProvider).count()
            print(f"✅ 提供商表查询成功，当前提供商数: {provider_count}")
            
            # 测试查询知识库
            kb_count = db.query(KnowledgeBase).count()
            print(f"✅ 知识库表查询成功，当前知识库数: {kb_count}")
            
            # 测试关联查询（手动方式）
            if kb_count > 0:
                kb = db.query(KnowledgeBase).first()
                if kb.provider_id:
                    provider = db.query(KnowledgeProvider).filter(
                        KnowledgeProvider.id == kb.provider_id
                    ).first()
                    
                    if provider:
                        print(f"✅ 找到知识库 {kb.id} 的提供商: {provider.name}")
                    else:
                        print(f"ℹ️ 知识库 {kb.id} 的提供商不存在")
                else:
                    print(f"ℹ️ 知识库 {kb.id} 没有设置提供商ID")
            
            return True
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crud_operations():
    """测试 CRUD 操作"""
    print("\n测试 CRUD 操作...")
    
    try:
        from app.db.session import get_db
        from app.crud.knowledge import knowledge_base, knowledge_provider
        
        # 获取数据库会话
        db = next(get_db())
        
        try:
            # 测试获取提供商
            providers = knowledge_provider.get_multi(db, limit=5)
            print(f"✅ CRUD 获取提供商成功，数量: {len(providers)}")
            
            # 测试获取知识库
            kbs = knowledge_base.get_multi(db, limit=5)
            print(f"✅ CRUD 获取知识库成功，数量: {len(kbs)}")
            
            # 测试通过ID获取提供商
            if providers:
                provider = knowledge_provider.get(db, provider_id=providers[0].id)
                if provider:
                    print(f"✅ CRUD 通过ID获取提供商成功: {provider.name}")
            
            return True
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ CRUD 操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("测试知识库关系修复")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        test_knowledge_imports,
        test_knowledge_models,
        test_database_operations,
        test_crud_operations
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试完成! 通过: {passed}/{total}")
    
    if passed == total:
        print("\n✅ 所有测试通过！知识库关系修复成功！")
        print("\n修复总结:")
        print("1. ✅ 移除了 KnowledgeBase 模型中的 provider 关系定义")
        print("2. ✅ 更新了 knowledge.py 中的代码，使用直接查询获取提供商")
        print("3. ✅ 所有模块可以正常导入和使用")
        print("4. ✅ 数据库操作正常，支持手动关联查询")
    else:
        print(f"\n❌ 有 {total - passed} 个测试失败，需要进一步检查")
