'use client'
import type { <PERSON> } from 'react'
import classNames from '@/utils/classnames'
import { publicBasePath } from '@/config'

type LogoSiteProps = {
  className?: string
}

const LogoSite: FC<LogoSiteProps> = ({
  className,
}) => {
  return (
    <img
      src={`${publicBasePath}/logo/logo.png`}
      className={classNames('block w-[22.651px] h-[24.5px]', className)}
      alt='logo'
    />
  )
}

export default LogoSite
